import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { getApi, getApiInline, putApi } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import {
  convertCurrencyToFloatDynamic,
  formatCurrency,
  formatDate,
  formatDateGlobaltoSimplified,
  formatToDateOnly,
} from "src/reusable/helpers";
import { toast } from "react-toastify";
import { ItemFilaAprovacaoType } from "../../types/ItemFilaAprovacaoType";
import {
  TermoJuridicoInfosType,
  ParcelaTermoStateType,
  TermoJuridicoApiResponse,
} from "../../types/TermoJuridicoTypes";
import { Editor } from "@tinymce/tinymce-react";

interface Props {
  isOpen: boolean;
  item: ItemFilaAprovacaoType | null;
  isJuridico: boolean;
  onClose: (updateData?: boolean) => void;
}

const EditTermoModal = ({ isOpen, onClose, item }: Props) => {
  const handleClose = () => {
    onClose();
  };

  const [loading, setLoading] = useState(false);
  const [tipoTermo, setTipoTermo] = useState([]);
  const [tipoTermoSelected, setTipoTermoSelected] = useState(null);

  // Form fields based on TermoJuridicoModal
  const [idInfo, setIdInfo] = useState(null);
  const [valorVencidas, setValorVencidas] = useState(0);
  const [valorVincendas, setValorVincendas] = useState(0);
  const [multaJuros, setMultaJuros] = useState(0);
  const [diferencaParcelas, setDiferencaParcelas] = useState(0);
  const [honorarios, setHonorarios] = useState(0);
  const [custas, setCustas] = useState(0);
  const [total, setTotal] = useState(0);
  const [qtdParcelas, setQtdParcelas] = useState(0);
  const [dataBase, setDataBase] = useState(new Date());
  const [valorAcordado, setValorAcordado] = useState(0);
  const [jurisdicaoAtual, setJurisdicaoAtual] = useState("");
  const [nrAtual, setNrAtual] = useState("");
  const [tipoAcao, setTipoAcao] = useState("");
  const [descricaoVeiculo, setDescricaoVeiculo] = useState("");
  const [grupoCotaContrato, setGrupoCotaContrato] = useState("");
  const [clientePrincipal, setClientePrincipal] = useState("");
  const [adversoPrincipal, setAdversoPrincipal] = useState("");
  const [nrParcelasVencidas, setNrParcelasVencidas] = useState("");
  const [nrParcelasVincendas, setNrParcelasVincendas] = useState("");
  const [parcelasGeradas, setParcelasGeradas] = useState<
    ParcelaTermoStateType[]
  >([]);
  const [clausulaExtra, setClausulaExtra] = useState("");

  const getTipoTermo = useCallback(async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTipoTermo(response);
      } else {
        setTipoTermo([]);
      }
    } catch (error) {
      console.error(error);
    }
  }, []);

  const loadExistingTermData = useCallback(async () => {
    if (!item?.id) return;

    try {
      const response: TermoJuridicoInfosType = await getApiInline(
        item.id,
        "getTermosInfos"
      );
      console.log("termo info", response);
      if (response) {
        setIdInfo(response.id);
        setJurisdicaoAtual(response.jurisdicaoAtual || "");
        setNrAtual(response.nrAtual || "");
        setClientePrincipal(response.clientePrincipal || "");
        setTipoAcao(response.tipoAcao || "");
        setAdversoPrincipal(response.adversoPrincipal || "");
        setGrupoCotaContrato(response.grupoCotaContrato || "");
        setNrParcelasVencidas(response.nrParcelasVencidas || "");
        setValorVencidas(response.valorParcelasVencidas || 0);
        setMultaJuros(response.multaJuros || 0);
        setCustas(response.custas || 0);
        setNrParcelasVincendas(response.nrParcelasVincendas || "");
        setValorVincendas(response.valorParcelasVincendas || 0);
        setHonorarios(response.honorarios || 0);
        setQtdParcelas(response.qtdParcelasAcordadas || 0);
        setValorAcordado(response.valorAcordado || 0);
        setDescricaoVeiculo(response.descricaoVeiculo || "");
        setClausulaExtra(response.extras || "");
        if (response.parcelas && response.parcelas.length > 0) {
          const parcelas = response.parcelas.map((p) => ({
            id: p.id,
            infosId: p.infosId,
            numero: p.numero,
            dtVencimento: new Date(p.vencimento + " 23:00:00"),
            valor: p.valor,
          }));
          setParcelasGeradas(parcelas.sort((a, b) => a.numero - b.numero));
        }
        setTipoTermoSelected(response.tipoTermoId);
      }
    } catch (error) {
      console.error("Could not load existing term data:", error);
      // This is expected if the endpoint doesn't exist yet
    }
  }, [item?.id]);

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo(), loadExistingTermData()]);
    setLoading(false);
  }, [getTipoTermo, loadExistingTermData]);

  useEffect(() => {
    asyncLoadFunc();
  }, [asyncLoadFunc]);

  // Calculate total when values change
  useEffect(() => {
    const calculatedTotal =
      valorVencidas +
      valorVincendas +
      multaJuros +
      diferencaParcelas +
      honorarios +
      custas;
    setTotal(calculatedTotal);
    setValorAcordado(calculatedTotal);
  }, [
    valorVencidas,
    valorVincendas,
    multaJuros,
    diferencaParcelas,
    honorarios,
    custas,
  ]);

  const handleQntParcelasChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!/^\d*$/.test(e.target.value)) return;
    const value = parseInt(e.target.value);
    setQtdParcelas(isNaN(value) ? 0 : value);
  };

  const handleParcelaChange = (index: number, field: string, value: string) => {
    const updatedParcelas = [...parcelasGeradas];
    if (field === "dtVencimento") {
      updatedParcelas[index].dtVencimento = new Date(value + " 23:00:00");
    } else if (field === "valor") {
      updatedParcelas[index].valor = convertCurrencyToFloatDynamic(value);
    }
    setParcelasGeradas(updatedParcelas);
  };

  const addNewParcela = () => {
    const lastParcela = parcelasGeradas[parcelasGeradas?.length - 1];
    const newParcela: ParcelaTermoStateType = {
      id: null,
      infosId: null,
      numero: (lastParcela?.numero ?? parcelasGeradas.length) + 1,
      dtVencimento: lastParcela?.dtVencimento
        ? new Date(
            new Date(lastParcela.dtVencimento).setMonth(
              lastParcela.dtVencimento.getMonth() + 1
            )
          )
        : new Date(),
      valor: lastParcela?.valor ?? 0,
    };
    setParcelasGeradas([...parcelasGeradas, newParcela]);
  };

  const removeParcela = (index: number) => {
    const updatedParcelas = parcelasGeradas.filter((_, i) => i !== index);
    // Renumber the remaining parcelas
    const renumberedParcelas = updatedParcelas.map((p, i) => ({
      ...p,
      numero: i + 1,
    }));
    setParcelasGeradas(renumberedParcelas);
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const payload = {
        id: idInfo,
        jurisdicaoAtual: jurisdicaoAtual,
        nrAtual: nrAtual,
        clientePrincipal: clientePrincipal,
        tipoAcao: tipoAcao,
        adversoPrincipal: adversoPrincipal,
        grupoCotaContrato: grupoCotaContrato,
        nrParcelasVencidas: nrParcelasVencidas,
        valorParcelasVencidas: valorVencidas,
        multaJuros: multaJuros,
        custas: custas,
        nrParcelasVincendas: nrParcelasVincendas,
        valorParcelasVincendas: valorVincendas,
        honorarios: honorarios,
        total: total,
        qtdParcelasAcordadas: qtdParcelas,
        valorAcordado: valorAcordado,
        descricaoVeiculo: descricaoVeiculo,
        extras: clausulaExtra,
        parcelas:
          parcelasGeradas?.map((p) => ({
            id: p.id,
            infosId: p.infosId,
            numero: p.numero,
            vencimento: formatToDateOnly(p.dtVencimento),
            valor: p.valor,
          })) ?? [],
      };

      const response = await putApi(payload, "putTermosInfos");

      if (response?.success === true) {
        toast.success("Termo atualizado com sucesso!");
        onClose(true);
      } else {
        toast.error("Erro ao atualizar termo!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao atualizar termo!");
    } finally {
      setLoading(false);
    }
  };

  const getTipoTermoSelected = () => {
    if (!tipoTermoSelected || tipoTermo.length === 0) return "";
    return ` - ${
      tipoTermo.find((x) => x.id === tipoTermoSelected)?.nome ?? ""
    }`;
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>
          Editar Termo Jurídico - {item?.nrContrato} {getTipoTermoSelected()}
        </h5>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading Title="Carregando" Msg="Aguarde..." />
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <div className="row mt-4">
            <div className="col-md-6">
              <label className="pt-1">Cliente Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={clientePrincipal}
                onChange={(e) => setClientePrincipal(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-6">
              <label className="pt-1">Adverso Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={adversoPrincipal}
                onChange={(e) => setAdversoPrincipal(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-4">
              <label className="pt-1">Grupo/Cota/Contrato:</label>
              <CInput
                className="mr-2 ml-2"
                value={grupoCotaContrato}
                readOnly
                onChange={(e) => setGrupoCotaContrato(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Nº Parcelas Vencidas:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrParcelasVencidas}
                onChange={(e) => setNrParcelasVencidas(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Nº Parcelas Vincendas:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrParcelasVincendas}
                onChange={(e) => setNrParcelasVincendas(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Valor Parcelas Vencidas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorVencidas, false)}
                onChange={(e) =>
                  setValorVencidas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Valor Parcelas Vincendas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorVincendas, false)}
                onChange={(e) =>
                  setValorVincendas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Honorários Advocatícios:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(honorarios, false)}
                onChange={(e) =>
                  setHonorarios(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Multa e Juros:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(multaJuros, false)}
                onChange={(e) =>
                  setMultaJuros(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Custas:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(custas, false)}
                onChange={(e) =>
                  setCustas(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Total:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(total, false)}
                readOnly
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Qtd Parcelas Acordadas:</label>
              <CInput
                className="mr-2 ml-2"
                value={qtdParcelas}
                onChange={handleQntParcelasChange}
                type="number"
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Valor Acordado:</label>
              <CInput
                className="mr-2 ml-2"
                value={formatCurrency(valorAcordado, false)}
                onChange={(e) =>
                  setValorAcordado(
                    convertCurrencyToFloatDynamic(e.currentTarget.value)
                  )
                }
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-3">
              <label className="pt-1">Jurisdição Atual:</label>
              <CInput
                className="mr-2 ml-2"
                value={jurisdicaoAtual}
                onChange={(e) => setJurisdicaoAtual(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Número Atual:</label>
              <CInput
                className="mr-2 ml-2"
                value={nrAtual}
                onChange={(e) => setNrAtual(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Tipo Ação:</label>
              <CInput
                className="mr-2 ml-2"
                value={tipoAcao}
                onChange={(e) => setTipoAcao(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Descrição Veículo:</label>
              <CInput
                className="mr-2 ml-2"
                value={descricaoVeiculo}
                onChange={(e) => setDescricaoVeiculo(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-12">
              <label htmlFor="descricao">Cláusula Extra:</label>

              <style>{`.tox-promotion { display: none !important; }`}</style>

              <Editor
                apiKey="0tikha1mpfegov516ubzkw3x7y9rm53pexmcj1if4s11cjcx"
                value={clausulaExtra}
                onEditorChange={(e: string) => {
                  setClausulaExtra(e);
                }}
                init={{
                  height: 300,
                  menubar: true,
                  plugins: [
                    "table",
                    "lists",
                    "link",
                    "image",
                    "code",
                    "advlist",
                    "autolink",
                    "fullscreen",
                  ],
                  toolbar:
                    "undo redo | blocks | bold italic underline | alignleft aligncenter alignright | bullist numlist | table | link image | code | fullscreen",
                  branding: false,
                }}
              />
            </div>
          </div>

          {/* Installments Section */}
          {parcelasGeradas.length > 0 && (
            <div className="row mt-4">
              <div className="col-md-12">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h6 className="mb-0">Parcelas do Termo:</h6>
                  <div>
                    <CButton
                      color="success"
                      size="sm"
                      className="mr-2"
                      onClick={addNewParcela}
                    >
                      + Adicionar Parcela
                    </CButton>
                  </div>
                </div>
                <CCard style={{ overflow: "auto", maxHeight: "200px" }}>
                  <CCardBody>
                    <div className="row">
                      <div className="col-md-2">
                        <label>Número:</label>
                      </div>
                      <div className="col-md-2">
                        <label>Data de vencimento:</label>
                      </div>
                      <div className="col-md-2">
                        <label>Valor:</label>
                      </div>
                      <div className="col-md-2">
                        <label>Ações:</label>
                      </div>
                    </div>
                    {parcelasGeradas.map((parcela, index) => (
                      <div className="row" key={index}>
                        <div className="col-md-2">
                          <span>{parcela.numero}</span>
                        </div>
                        <div className="col-md-2 input-group-sm">
                          <input
                            className="form-control mt-1"
                            value={formatDateGlobaltoSimplified(
                              parcela.dtVencimento
                            )}
                            onChange={(e) =>
                              handleParcelaChange(
                                index,
                                "dtVencimento",
                                e.target.value
                              )
                            }
                            type="date"
                          />
                        </div>
                        <div className="col-md-2 input-group-sm">
                          <input
                            className="form-control mt-1"
                            value={formatCurrency(parcela.valor, false)}
                            onChange={(e) =>
                              handleParcelaChange(
                                index,
                                "valor",
                                e.target.value
                              )
                            }
                          />
                        </div>
                        <div className="col-md-2">
                          <CButton
                            color="danger"
                            size="sm"
                            className="mt-1"
                            onClick={() => removeParcela(index)}
                            disabled={parcelasGeradas.length <= 1}
                          >
                            Remover
                          </CButton>
                        </div>
                      </div>
                    ))}
                  </CCardBody>
                </CCard>
              </div>
            </div>
          )}
        </CModalBody>
      )}
      <CModalFooter>
        <CButton
          color="success"
          className="mr-2"
          onClick={handleSave}
          disabled={loading}
        >
          Salvar Alterações
        </CButton>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Cancelar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default EditTermoModal;
