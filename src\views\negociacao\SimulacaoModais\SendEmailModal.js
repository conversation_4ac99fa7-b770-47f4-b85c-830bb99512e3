import React, { useEffect, useState } from "react";
import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCardFooter,
  CCol,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalHeader,
  CModalTitle,
} from "@coreui/react";
import Select from "react-select";
import { getApi, postApi } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import { formatDocument } from "src/reusable/helpers";
import { postManager } from "src/reusable/helpers.js";
import { token } from "src/api";

const SendEmailModal = ({ show, handleClose, msg, em = "" }) => {
  const logged = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [textMsg, setTextmsg] = useState(msg);
  const [loading, setLoading] = useState(false);
  const [loadingContent, setLoadingContent] = useState(false);
  const [email, setEmail] = useState(em);
  const [subject, setSubject] = useState(
    financiadoData?.nome?.toUpperCase() +
    " - " +
    formatDocument(financiadoData?.cpfCnpj)
  );

  const mountAsync = async () => {
    setLoadingContent(true);
    let users = await getApi(null, "getAllUsersSimplified");
    if (users?.length > 0) {

      users = await Promise.all(users.map(async (user) => {
        user.email = await postManager(token(), user.email, 2);
        user.name = await postManager(token(), user.name, 2);
        return user;
      }));

      for (const user of users) {
        user.label = user.name;
        user.value = user.id;
      }
      setUsers(users);
      setSelectedUsers([...users.filter((user) => user.id === logged?.id)]);
    }
    setLoadingContent(false);
  };

  useEffect(() => {
    mountAsync();
  }, []);

  const handleUsersChange = (selectedOptions) => {
    const hasLogged = selectedOptions.find((user) => user.id === logged?.id);
    if (hasLogged === undefined || hasLogged === null) return;
    setSelectedUsers(selectedOptions);
  };

  const sendEmail = async () => {
    setLoading(true);
    try {
      const payload = {
        email: email,
        subject: subject,
        emailCopy: selectedUsers.flatMap((x) => x.email),
        message: textMsg,
      };
      const res = await postApi(payload, "postEmailSimulation");
      if (res?.success === true) {
        toast.success("Email enviado com sucesso");
        handleClose();
      }
      if (res?.success === false) toast.error("Erro ao enviar email");
    } catch (err) {
      console.error(err);
      toast.error("Erro ao enviar email");
    }
    setLoading(false);
  };

  return (
    <CModal
      show={show}
      onClose={handleClose}
      size="lg"
      closeOnBackdrop={false}
      backdrop={false}
    >
      <CModalHeader closeButton>
        <CModalTitle>Enviar email</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CCard>
          <CCardBody>
            {loadingContent && <LoadingComponent />}
            {!loadingContent && (
              <CCol>
                <CLabel className={"mt-2"}>Email</CLabel>
                <CInput
                  onChange={(event) => setEmail(event.target.value)}
                  value={email}
                />
                <CLabel className={"mt-2"}>Usuários Copiados</CLabel>
                <Select
                  isMulti
                  options={users}
                  value={selectedUsers}
                  onChange={handleUsersChange}
                />
                <CLabel className={"mt-2"}>Assunto</CLabel>
                <CInput
                  value={subject}
                  onChange={(event) => setSubject(event.target.value)}
                />
                <CLabel className={"mt-2"}>Mensagem</CLabel>
                <textarea
                  style={{
                    width: "100%",
                    minHeight: "150px",
                    borderRadius: "5px",
                    padding: "10px",
                  }}
                  placeholder=" Insira aqui sua mensagem."
                  value={textMsg}
                  onChange={(event) => setTextmsg(event.target.value)}
                // rows={5}
                />
              </CCol>
            )}
          </CCardBody>
          <CCardFooter className={"text-right"}>
            {loading && <LoadingComponent />}
            {!loading && (
              <>
                <CButton color="primary" className={"mr-2"} onClick={sendEmail}>
                  Enviar
                </CButton>
                <CButton color="danger" onClick={handleClose}>
                  Cancelar
                </CButton>
              </>
            )}
          </CCardFooter>
        </CCard>
      </CModalBody>
    </CModal>
  );
};

export default SendEmailModal;
