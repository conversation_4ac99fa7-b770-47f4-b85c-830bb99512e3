import React, { useState, useEffect, useRef } from "react";
import { CLabel, CCol, CInput, CSwitch, CForm } from "@coreui/react";
import { CButton, CModal, CModalBody, CModalFooter, CModalHeader, CModalTitle, CFormGroup } from "@coreui/react";
import Select from "react-select";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import { token } from "src/api";

const CreateTopicDocumentModal = ({ isOpen, editDocument, topicId, onClose }) => {
  
  const [isLoading, setIsLoading] = useState(false);
  const [documentType, setDocumentType] = useState({ id: "", label: "" });
  const [documento, setDocument] = useState({ topicId: topicId, id: null, title: "", description: "", documentTypeId: "", filePath: "", active: true });
  const [labelStatus, setLabelStatus] = useState("Ativo");
  const editorRef = useRef(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setDocument((prevDocument) => ({ ...prevDocument, [name]: value }));
  };

  const handleStatusToggle = () => {
    setDocument((prevDocument) => ({ ...prevDocument, active: !prevDocument.active }));
  };

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const requiredFields = [
    { name: "title", displayName: "Título" },
    { name: "description", displayName: "Descrição" },
    { name: "documentTypeId", displayName: "Tipo da Inf." },
  ];

  const documentTypeOptions = [{ id: "Nofile", label: "Sem arquivo" }, { id: "Pdf", label: "Arquivo em Pdf" }, { id: "Video", label: "Arquivo de Video" }, { id: "Image", label: "Arquivo de Imagem" }];

  const handleDocumentTypeSelect = (selectedOption) => {
    setDocumentType(selectedOption);
    setDocument((prevDocument) => ({ ...prevDocument, documentTypeId: selectedOption.id }));
  };

  const handleFilePathChange = async (event) => {
    const file = event.target.files[0];
    const allowedTypes = ["application/pdf", "application/msword", "video/mp4", "image/jpeg"];
    if (!allowedTypes.includes(file.type)) {
      alert("Tipo de arquivo não permitido.");
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      alert("Arquivo excede o tamanho máximo permitido de 5MB.");
      return;
    }

    await handleUploadImageDocument(document, file.fileName, file);
  };

  const handleDocumentSubmit = (e) => {
    e.preventDefault();

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = documento[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    setIsLoading(true);
    const newDocument = {
      title: documento.title,
      description: documento.description,
      topicId: topicId,
      documentTypeId: documento.documentoTypeId,
      filePath: documento.filePath ?? "",
      active: documento.active
    };

    handleCreateTopicDocument(newDocument);
  };

  const handleDocumentEdit = (e) => {
    e.preventDefault();

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = documento[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const editedDocument = {
      id: documento.id,
      title: documento.title,
      description: documento.description,
      topicId: documento?.topicId ?? topicId,
      documentTypeId: documento.documentoTypeId,
      filePath: documento.filePath ?? "",
      active: documento.active
    };

    handleUpdateTopic(editedDocument);
    resetModal();
    onClose();
  };

  const handleCreateTopicDocument = async (newTopic) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/topicdocument`;

      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}` },
        body: JSON.stringify(newTopic),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Item do tópico criado com sucesso");
          editDocument = status.data;
          resetModal();
          setIsLoading(false);
        } else {
          toast.warning("Item do tópico já cadastrado");
          setIsLoading(false);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro cadastrando o Item do tópico:", error);
      setIsLoading(false);
    }
  };


  const handleUploadImageDocument = async (documento, fileName, file) => {
    try {
      const url = `${getURI()}/File/upload?topicId=${documento.topicId}`;

      const formData = new FormData();
      formData.append("formFile", file);
      formData.append("fileName", fileName);

      const response = await fetch(url, {
        method: "POST",
        headers: { Authorization: `Bearer ${token()}` },
        body: formData,
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Arquivo carregado com sucesso");
          setDocument((prevDocument) => ({ ...prevDocument, filePath: `${getURI()}/File/${status.filePath}` }));
        } else {
          toast.warning("Falha ao carregar Arquivo");
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro ao carregar Arquivo", error);
    }
  };


  const handleUpdateTopic = async (topic) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/topicdocument`;

      const response = await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}`, },
        body: JSON.stringify({
          ...topic,
          id: topic.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          setIsLoading(false);
          handleClose();
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro editando o Tópico:", error);
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  function resetModal() {
    setDocument({ topicId: topicId, id: null, title: "", description: "", documentTypeId: "", filePath: "", active: true });
  }

  useEffect(() => {
    if (isOpen && editDocument) {
      setDocument({
        id: editDocument.id,
        title: editDocument.title,
        description: editDocument.description,
        topicId: editDocument.topicId ?? topicId,
        documentTypeId: editDocument.documentTypeId,
        filePath: editDocument.filePath ?? "",
        active: editDocument.active
      });
      setDocumentType(documentTypeOptions.filter(a => a.id === editDocument.documentTypeId)[0]);
    } else resetModal();
  }, [isOpen, editDocument]);

  useEffect(() => {
    setLabelStatus(documento.active === true ? "Ativo" : "Inativo");
  }, [documento.active]);

  const applyFormat = (command, value = null) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
    }
  };

  return (
    <>
      <CModal
        show={isOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="xl"
      >
        <CModalHeader closeButton>
          <CModalTitle>{editDocument ? "Editar Documento" : "Adicionar Documento"}</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="title">Título</CLabel>
                  <CInput
                    id="title"
                    name="title"
                    placeholder="Informe o Título"
                    autoComplete="off"
                    required
                    value={documento.title}
                    onChange={handleInputChange}
                    maxLength="50"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="description">Descrição</CLabel>
                  <div className="mb-2 d-flex gap-2">
                    <CButton size="sm" color="primary" onClick={() => applyFormat("bold")}>
                      <i className="cil-bold"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("italic")}>
                      <i className="cil-italic"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("underline")}>
                      <i className="cil-underline"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyLeft")}>
                      <i className="cil-align-left"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyCenter")}>
                      <i className="cil-align-center"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyRight")}>
                      <i className="cil-align-right"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyFull")}>
                      <i className="cil-notes"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "5")}>
                      <i className="cil-text-square"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "3")}>
                      <i className="cil-text-size"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("insertParagraph")}>
                      <i className="cil-paragraph"></i>
                    </CButton>
                  </div>
                  <div
                    ref={editorRef}
                    contentEditable
                    id="description"
                    name="description"
                    style={{
                      border: "1px solid #ced4da",
                      borderRadius: "0.25rem",
                      padding: "0.5rem",
                      minHeight: "100px",
                      whiteSpace: "pre-wrap",
                    }}
                    onInput={(e) => handleInputChange({ target: { name: 'description', value: e.target.innerHTML } })}
                    dangerouslySetInnerHTML={{ __html: documento.description }}
                  ></div>
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="documentTypeId">Tipo da Informação</CLabel>
                  <Select
                    id="documentTypeId"
                    name="documentTypeId"
                    options={documentTypeOptions}
                    value={documentType}
                    onChange={handleDocumentTypeSelect}
                    placeholder="Selecione o Tipo da informação"
                    autoComplete="off"
                    required
                  />
                </CCol>
                {(documentType?.id !== 'Nofile') && (
                  <CCol>
                    <CLabel htmlFor="filePath">Carregar um arquivo</CLabel>
                    <CInput
                      type="file"
                      id="filePath"
                      name="filePath"
                      accept=".doc,.pdf,.mp4,.jpg"
                      onChange={handleFilePathChange}
                      required
                    />
                  </CCol>
                )}
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="status" style={{ paddingRight: "20px" }}>Status</CLabel>
                  <CSwitch
                    id="status"
                    name="status"
                    color="success"
                    checked={documento.active}
                    onChange={handleStatusToggle}
                    shape="pill"
                    size="sm"
                  />
                  <CLabel style={{ paddingLeft: "20px" }}> {labelStatus} </CLabel>
                </CCol>
              </CFormGroup>
            </CForm>
          )}
        </CModalBody>
        <CModalFooter>
          {!editDocument?.id && (
            <CButton color="info" onClick={handleDocumentSubmit}>
              <i className="cil-plus"></i> Adic. Documento
            </CButton>
          )}
          {editDocument?.id && (
            <>
              <CButton color="info" onClick={handleDocumentEdit}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>

    </>
  );
};

export default CreateTopicDocumentModal;
