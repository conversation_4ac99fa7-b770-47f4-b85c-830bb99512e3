import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>odal<PERSON>oot<PERSON>,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CRow,
  CCol,
  CCardBody,
  CButton,
  CLabel,
} from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import CardLoading from "src/reusable/CardLoading";
import Ticket<PERSON>hart from "./TicketChart.tsx";
import { getApi} from "src/reusable/functions.js";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { GET_DATA, POST_FORMDATA, token } from "src/api.js";
import { getURI } from "src/config/apiConfig.js";
import Select from "react-select";
import TicketDetails from "./TicketDetails.js";
import { toast } from "react-toastify";
import LoadingComponent from "src/reusable/Loading.js";
import PixDetails from "./PixDetails.js";

const bgColor = [
  "#e55353",
  "#2eb85c",
  "#f9b115",
  "#ff6384",
  "#4bc09b",
  "#36a2eb",
  "#00D8FF",
  "#DD1B16",
  "#c9cbcf",
];

const today = new Date();

const PostFormData = async (formData, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_FORMDATA(
        getURI(endpoint),
        formData,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const TicketChartModal = ({ onClose }) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [selectDateBegin, setSelectDateBegin] = useState(today);
  const [selectDateEnd, setSelectDateEnd] = useState(today);
  const [selectedClient, setSelectedClient] = useState(null);
  const [selectCrm, setSelectCrm] = useState({
    datacobName: user?.activeConnection,
    datacobNumber: user?.activeConnection,
  });

  const [showDetails, setShowDetails] = useState(false);
  const [showDetailsPix, setShowDetailsPix] = useState(false);
  const [selectedTickets, setSelectedTickets] = useState(null);
  const [selectedPix, setSelectedPix] = useState(null);

  const [usersCrm, setUsersCrm] = useState([]);
  const [loadingUsersCrm, setLoadingUsersCrm] = useState(false);
  const [selectedUserCrm, setSelectedUserCrm] = useState(null);

  const [loadingExport, setLoadingExport] = useState(false);

  const getTicketsData = async () => {
    setSearch(true);
    setLoading(true);
    setSelectedClient(null);
    try {
      const result = await GET_DATA(
        getURI("getTicketsUser") +
          `${selectedUserCrm !== null ? "/" + selectedUserCrm.id_Usuario : ""}`,
        {
          crm: selectCrm?.datacobNumber,
          inicio: selectDateBegin.toISOString().substring(0, 10),
          fim: selectDateEnd.toISOString().substring(0, 10),
        },
        true
      );
      if (result.length > 0) {
        const res = [];
        const todos = {
          nomeCliente: "Todos",
          boletos: [],
          pix: [],
          dataset: [],
          labels: [
            "Boletos Cancelados",
            "Boletos Pagos",
            "Boletos Abertos",
            "Pix Cancelados",
            "Pix Pagos",
            "Pix Abertos",
          ],
        };
        for (let i in result) {
          const ticket = result[i];

          todos.boletos = [...todos.boletos, ...ticket.boletos];
          todos.pix = [...todos.pix, ...ticket.pix];

          const item = {
            nomeCliente: "",
            boletos: [],
            pix: [],
            dataset: [],
            labels: [
              "Boletos Cancelados",
              "Boletos Pagos",
              "Boletos Abertos",
              "Pix Cancelados",
              "Pix Pagos",
              "Pix Abertos",
            ],
          };

          item.nomeCliente = ticket.nomeCliente;
          const cancelados = ticket.boletos.filter(
            (x) => x.statusBoleto === "C"
          ).length;
          const pagos = ticket.boletos.filter(
            (x) => x.statusBoleto === "P"
          ).length;
          const abertos = ticket.boletos.filter(
            (x) => x.statusBoleto === "A"
          ).length;
          const pixCancelados = ticket.pix.filter(
            (x) => x.statusPix === "C"
          ).length;
          const pixPagos = ticket.pix.filter((x) => x.statusPix === "P").length;
          const pixAbertos = ticket.pix.filter(
            (x) => x.statusPix === "A"
          ).length;
          item.dataset.push({
            backgroundColor: bgColor,
            data: [
              cancelados,
              pagos,
              abertos,
              pixCancelados,
              pixPagos,
              pixAbertos,
            ],
          });
          item.boletos = ticket.boletos;
          item.pix = ticket.pix;
          res.push(item);
        }

        const canceladosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "C"
        ).length;
        const pagosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "P"
        ).length;
        const abertosTodos = todos.boletos.filter(
          (x) => x.statusBoleto === "A"
        ).length;
        const pixCanceladosTodos = todos.pix.filter(
          (x) => x.statusPix === "C"
        ).length;
        const pixPagosTodos = todos.pix.filter(
          (x) => x.statusPix === "P"
        ).length;
        const pixAbertosTodos = todos.pix.filter(
          (x) => x.statusPix === "A"
        ).length;

        todos.dataset.push({
          backgroundColor: bgColor,
          data: [
            canceladosTodos,
            pagosTodos,
            abertosTodos,
            pixCanceladosTodos,
            pixPagosTodos,
            pixAbertosTodos,
          ],
        });
        setTickets([todos, ...res]);
      } else {
        setTickets([]);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  const getUsersTu = async () => {
    setLoadingUsersCrm(true);
    try {
      const res = await getApi(
        {
          activeConnection: selectCrm?.datacobNumber,
        },
        "getDatacobUsers"
      );
      setUsersCrm(res === null ? [] : res);
    } catch (e) {
      setUsersCrm([]);
      console.error(e);
    }
    setLoadingUsersCrm(false);
  };

  const [isAllowedExport, setIsAllowedExport] = useState(false);
  async function getAllowedExportRoles() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "exportar_boletos_funcoes"
    );
    try {
      const arrayRoles = JSON.parse(response)?.find(
        (x) => x === user?.role?.id
      );
      if (arrayRoles !== undefined) setIsAllowedExport(true);
      else setIsAllowedExport(false);
    } catch (err) {
      setIsAllowedExport(false);
    }
  }

  useEffect(() => {
    if (user?.isAdmin === true) {
      getUsersTu();
    }
    getAllowedExportRoles();
  }, [selectCrm]);

  const handleShowDetails = (selTickets, status = "") => {
    if (status !== "") {
      const filter = selTickets.boletos.filter(
        (x) => x.statusBoleto === status
      );
      setSelectedTickets(filter);
    } else {
      setSelectedTickets(selTickets?.boletos);
    }
    setShowDetails(true);
  };

  const handleShowDetailsPix = (sel, status = "") => {
    if (status !== "") {
      const filter = sel.pix.filter((x) => x.statusPix === status);
      setSelectedPix(filter);
    } else {
      setSelectedPix(sel?.pix);
    }
    setShowDetailsPix(true);
  };

  const handleCloseDetails = () => {
    setSelectedTickets(null);
    setShowDetails(false);
    setSelectedPix(null);
    setShowDetailsPix(false);
  };

  const handleSelectCrm = (e) => {
    setSelectCrm(e);
  };

  const handleExportPdf = async (e) => {
    setLoadingExport(true);
    try {
      const url = getURI(
        showDetailsPix
          ? "postPixUserPdf"
          : showDetails
          ? "postTicketsUserPdf"
          : ""
      );
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(
          showDetailsPix ? selectedPix : showDetails ? selectedTickets : {}
        ),
      });

      // Cria uma URL para o Blob
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "registros.pdf";
        document.body.appendChild(link); // Necessário para o Firefox
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url); // Limpa o objeto URL
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    }
    setLoadingExport(false);
  };

  const handleExportExcel = async (e) => {
    setLoadingExport(true);
    try {
      const url = getURI(
        showDetailsPix
          ? "postPixUserExcel"
          : showDetails
          ? "postTicketsUserExcel"
          : ""
      );
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(
          showDetailsPix ? selectedPix : showDetails ? selectedTickets : {}
        ),
      });

      // Cria uma URL para o Blob
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "registros.xlsx";
        document.body.appendChild(link); // Necessário para o Firefox
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url); // Limpa o objeto URL
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    }
    setLoadingExport(false);
  };

  return (
    <CModal
      show={true}
      onClose={onClose}
      className={"custom-modal-2"}
      closeOnBackdrop={false}
      size="xl"
    >
      <CModalHeader>
        <CModalTitle>Visão de Boletos</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          {!showDetails && !showDetailsPix && (
            <CCol>
              <CCardBody className="py-1">
                <CRow>
                  <CCol md={2}>
                    <CLabel>Data Inicio</CLabel> <br />
                    <ReactDatePicker
                      selected={selectDateBegin}
                      onChange={(e) => setSelectDateBegin(e)}
                      className="form-control w-100"
                      dateFormat="dd/MM/yyyy"
                      onKeyDown={(e) => e.preventDefault()}
                    />
                  </CCol>
                  <CCol md={2}>
                    <CLabel>Data Fim</CLabel> <br />
                    <ReactDatePicker
                      selected={selectDateEnd}
                      onChange={(e) => setSelectDateEnd(e)}
                      className="form-control"
                      dateFormat="dd/MM/yyyy"
                      onKeyDown={(e) => e.preventDefault()}
                    />
                  </CCol>
                  {user?.datacobs?.length > 1 && (
                    <CCol md={2} style={{ color: "black" }}>
                      <CLabel>CRM</CLabel> <br />
                      <Select
                        options={user.datacobs}
                        onChange={handleSelectCrm}
                        getOptionLabel={(opt) => opt.datacobNumber}
                        getOptionValue={(opt) => opt.datacobNumber}
                        placeholder={"Selecione"}
                        value={selectCrm}
                        height="500px"
                      />
                    </CCol>
                  )}
                  {user?.isAdmin === true && (
                    <CCol md={3} style={{ color: "black" }}>
                      {loadingUsersCrm ? (
                        <div style={{ color: "black" }}>
                          <CardLoading />
                        </div>
                      ) : (
                        <>
                          <CLabel>Usuário</CLabel> <br />
                          <Select
                            options={usersCrm}
                            value={selectedUserCrm}
                            getOptionLabel={(opt) => opt.nome}
                            getOptionValue={(opt) => opt.id_Usuario}
                            onChange={(e) => setSelectedUserCrm(e)}
                            placeholder={"Selecione"}
                          />
                        </>
                      )}
                    </CCol>
                  )}
                  <CCol md={2}>
                    <CButton
                      color="primary"
                      style={{ marginTop: "31px" }}
                      onClick={getTicketsData}
                    >
                      Buscar
                    </CButton>
                  </CCol>
                </CRow>
                <br />
                {search && (
                  <>
                    {loading ? (
                      <div style={{ color: "black" }}>
                        <CardLoading />
                      </div>
                    ) : (
                      <>
                        {tickets.length > 0 && (
                          <>
                            <div style={{ color: "black" }}>
                              <Select
                                options={tickets}
                                onChange={(e) => setSelectedClient(e)}
                                getOptionLabel={(option) => option.nomeCliente}
                                getOptionValue={(opt) => opt.nomeCliente}
                                placeholder={"Selecione o cliente..."}
                              />
                            </div>
                            <br />
                            {selectedClient && (
                              <>
                                <CRow>
                                  <CCol md={8}>
                                    <TicketChart
                                      Datasets={selectedClient.dataset}
                                      Labels={selectedClient.labels}
                                      displayTitle={true}
                                      legendPosition="bottom"
                                      title={selectedClient.nomeCliente}
                                    />
                                  </CCol>
                                  <CCol md={4} className={"d-flex flex-column"}>
                                    <CButton
                                      color="danger"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "C")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[0]}{" "}
                                      Boletos Cancelados
                                    </CButton>
                                    <CButton
                                      color="success"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "P")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[1]}{" "}
                                      Boletos Pagos
                                    </CButton>
                                    <CButton
                                      color="warning"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient, "A")
                                      }
                                    >
                                      {selectedClient.dataset[0].data[2]}{" "}
                                      Boletos Abertos
                                    </CButton>
                                    <CButton
                                      color="primary"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetails(selectedClient)
                                      }
                                    >
                                      {selectedClient.boletos.length} Boletos
                                    </CButton>
                                    <CButton
                                      className="mr-2 mt-2"
                                      style={{
                                        backgroundColor: "#ff6384",
                                        borderColor: "#ff6384",
                                        color: "white",
                                      }}
                                      onMouseEnter={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#fb4f73")
                                      }
                                      onMouseLeave={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#ff6384")
                                      }
                                      onClick={() =>
                                        handleShowDetailsPix(
                                          selectedClient,
                                          "C"
                                        )
                                      }
                                    >
                                      {selectedClient.dataset[0].data[3]} Pix
                                      Cancelados
                                    </CButton>
                                    <CButton
                                      style={{
                                        backgroundColor: "#4bc09b",
                                        borderColor: "#46b592",
                                        color: "white",
                                      }}
                                      onMouseEnter={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#309e7b")
                                      }
                                      onMouseLeave={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#4bc09b")
                                      }
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetailsPix(
                                          selectedClient,
                                          "P"
                                        )
                                      }
                                    >
                                      {selectedClient.dataset[0].data[4]} Pix
                                      Pagos
                                    </CButton>
                                    <CButton
                                      style={{
                                        backgroundColor: "#36a2eb",
                                        borderColor: "#36a2eb",
                                        color: "white",
                                      }}
                                      onMouseEnter={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#3395d7")
                                      }
                                      onMouseLeave={(e) =>
                                        (e.target.style.backgroundColor =
                                          "#36a2eb")
                                      }
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetailsPix(
                                          selectedClient,
                                          "A"
                                        )
                                      }
                                    >
                                      {selectedClient.dataset[0].data[5]} Pix
                                      Abertos
                                    </CButton>
                                    <CButton
                                      color="primary"
                                      className="mr-2 mt-2"
                                      onClick={() =>
                                        handleShowDetailsPix(selectedClient)
                                      }
                                    >
                                      {selectedClient.pix.length} Pix
                                    </CButton>
                                  </CCol>
                                </CRow>
                              </>
                            )}
                          </>
                        )}
                        {(tickets === null || tickets.length === 0) && (
                          <div className="text-center">
                            <h3>Nenhum boleto encontrado</h3>
                          </div>
                        )}
                      </>
                    )}
                  </>
                )}
              </CCardBody>
            </CCol>
          )}
          {showDetails && selectedTickets !== null && (
            <TicketDetails
              tickets={selectedTickets}
              onClose={handleCloseDetails}
            />
          )}
          {showDetailsPix && selectedPix !== null && (
            <PixDetails tickets={selectedPix} onClose={handleCloseDetails} />
          )}
        </CRow>
      </CModalBody>
      <CModalFooter className={"justify-content-center"}>
        {(showDetails || showDetailsPix) &&
          (selectedTickets !== null || showDetailsPix !== null) && (
            <CRow>
              {loadingExport ? (
                <div style={{ color: "black" }}>
                  <LoadingComponent />
                </div>
              ) : (
                <>
                  <CButton color="secondary" onClick={handleCloseDetails}>
                    Voltar
                  </CButton>
                  {isAllowedExport && (
                    <>
                      <CButton
                        color="info"
                        className={"ml-2"}
                        onClick={handleExportPdf}
                      >
                        Exportar PDF
                      </CButton>
                      <CButton
                        color="primary"
                        className={"ml-2"}
                        onClick={handleExportExcel}
                      >
                        Exportar Excel
                      </CButton>
                    </>
                  )}
                </>
              )}
            </CRow>
          )}
        {!showDetails && !showDetailsPix && selectedTickets === null && (
          <CButton color="secondary" onClick={onClose}>
            Fechar
          </CButton>
        )}
      </CModalFooter>
    </CModal>
  );
};

export default TicketChartModal;
