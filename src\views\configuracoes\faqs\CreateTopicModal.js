import React, { useState, useEffect, useRef } from "react";
import { CLabel, CCol, CInput, CSwitch, CForm, CCard, CCardBody } from "@coreui/react";
import { CButton, CModal, CModalBody, CModalFooter, CModalHeader, CModalTitle, CFormGroup, CBadge } from "@coreui/react";
import TableSelectItens from "src/reusable/TableSelectItens";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import CreateTopicDocumentModal from "./CreateTopicDocumentModal";
import { token } from "src/api";

const CreateTopicModal = ({ isOpen, editTopic, onClose }) => {
  const [topic, setTopic] = useState({ id: null, name: "", description: "", active: true });
  const [isLoading, setIsLoading] = useState(false);
  const [labelStatus, setLabelStatus] = useState("Ativo");

  /* documents */
  const [documentList, setDocumentList] = useState([]);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isModalDocumentOpen, setIsModalDocumentOpen] = useState(false);

  const editorRef = useRef(null);

  const handleInputNameChange = (e) => {
    const { name, value } = e.target;
    setTopic((prevTopic) => ({ ...prevTopic, [name]: value }));
  };

  const handleInputDescriptionChange = (e) => {
    const { name, value } = e.target;
    setTopic((prevTopic) => ({ ...prevTopic, [name]: value }));
  };

  const handleStatusToggle = () => {
    setTopic((prevTopic) => ({ ...prevTopic, active: !prevTopic.active }));
  };

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const requiredFields = [
    { name: "name", displayName: "Título" },
    { name: "description", displayName: "Descrição" },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = topic[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const newTopic = {
      name: topic.name,
      description: topic.description,
      active: topic.active,
    };

    handleCreateTopic(newTopic);
  };

  const handleCreateTopic = async (newTopic) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/topic`;

      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}` },
        body: JSON.stringify(newTopic),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Tópico criado com sucesso");
          editTopic = status.data;
          setTopic(status.data);
          setIsLoading(false);
        } else {
          toast.warning("Tópico já cadastrado");
          setIsLoading(false);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro cadastrando o Tópico:", error);
      setIsLoading(false);
    }
  };

  const handleUpdateTopic = async (topic) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/topic`;

      const response = await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}`, },
        body: JSON.stringify({
          ...topic,
          id: topic.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          setIsLoading(false);
          handleClose();
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro editando o Tópico:", error);
      setIsLoading(false);
    }
  };

  const handleEdit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = topic[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const editedTopic = {
      id: topic.id,
      name: topic.name,
      description: topic.description,
      active: topic.active,
    };

    handleUpdateTopic(editedTopic);
  };

  const handleClose = () => {
    onClose();
  };

  function resetModal() {
    setTopic({
      id: null,
      name: "",
      description: "",
      active: true,
    });
  }

  useEffect(() => {
    if (isOpen && editTopic) {
      setTopic({
        id: editTopic?.id,
        name: editTopic.name,
        description: editTopic.description,
        active: editTopic.active,
      });

      setDocumentList(editTopic?.topicDocuments)
    } else resetModal();
  }, [isOpen, editTopic]);

  useEffect(() => {
    setLabelStatus(topic?.active === true ? "Ativo" : "Inativo");
  }, [topic?.active]);

  /* documents */

  const fields = [
    {
      key: "title",
      label: "Título",
      defaultSort: "desc",
      defaultSortColumn: true
    },
    {
      key: "description",
      label: "Descrição"
    },
    {
      key: "documentTypeId",
      label: "Tipo Inf.",
      formatterByObject: (item) => handleViewType(item),
    },
    {
      key: "active",
      label: "Status",
      formatterByObject: (item) => handleViewActive(item),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => handleViewActions(item),
    },
  ];

  const handleViewActive = (item) => {
    return item && (typeof item?.active !== "undefined") && item.active ? (
      <CBadge color="success">Ativo</CBadge>
    ) : (
      <CBadge color="danger">Inativo</CBadge>
    );
  };

  const handleViewType = (item) => {
    if (!item || typeof item.documentTypeId === "undefined") {
      return null;
    }

    const badgeTypes = {
      Nofile: { color: "danger", text: "Sem arquivo" },
      Video: { color: "success", text: "Vídeo" },
      Pdf: { color: "info", text: "PDF" },
      Doc: { color: "primary", text: "Doc" },
      Image: { color: "secondary", text: "Imagem" },
    };

    const badge = badgeTypes[item.documentTypeId];
    return badge ? <CBadge color={badge.color}>{badge.text}</CBadge> : null;
  };

  const handleViewActions = (item) => {
    return (
      <>
        <CButton
          color="secondary"
          onClick={() => handleDocumentEdit(item)}
        /*disabled={
          !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
        }*/
        >
          <i className="cil-pencil"></i>
        </CButton>
      </>
    );
  };

  const handleCreate = () => {
    setSelectedDocument(null);
    setIsModalDocumentOpen(true);
  };

  const handleCloseDocumenteModal = async () => {
    await getTopicDocuments();
    setIsModalDocumentOpen(false);
  }

  const getTopicDocuments = async () => {
    try {
      const response = await fetch(`${getURI()}/topicdocument/?topicId=${topic?.id}`, {
        headers: { Authorization: `Bearer ${token()}` }
      });

      if (response.ok) {
        const data = await response.json();
        setDocumentList(data.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const handleDocumentEdit = (document) => {
    setSelectedDocument(document);
    setIsModalDocumentOpen(true);
  };

  /* End documents */

  const applyFormat = (command, value = null) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
    }
  };

  return (
    <>
      <CModal
        show={isOpen && !isModalDocumentOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>
          <CModalTitle>{(topic?.id ?? false) ? "Editar Tópico" : "Adicionar Tópico"}</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="name">Título</CLabel>
                  <CInput
                    id="name"
                    name="name"
                    placeholder="Informe o Tópico"
                    autoComplete="off"
                    required
                    value={topic.name}
                    onChange={handleInputNameChange}
                    maxLength="50"
                  />
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="description">Descrição</CLabel>
                  <div className="mb-2 d-flex gap-2">
                    <CButton size="sm" color="primary" onClick={() => applyFormat("bold")}>
                      <i className="cil-bold"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("italic")}>
                      <i className="cil-italic"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("underline")}>
                      <i className="cil-underline"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyLeft")}>
                      <i className="cil-align-left"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyCenter")}>
                      <i className="cil-align-center"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyRight")}>
                      <i className="cil-align-right"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("justifyFull")}>
                      <i className="cil-notes"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "5")}>
                      <i className="cil-text-square"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "3")}>
                      <i className="cil-text-size"></i>
                    </CButton>
                    <CButton size="sm" color="primary" onClick={() => applyFormat("insertParagraph")}>
                      <i className="cil-paragraph"></i>
                    </CButton>
                  </div>
                  <div
                    ref={editorRef}
                    contentEditable
                    id="description"
                    name="description"
                    style={{
                      border: "1px solid #ced4da",
                      borderRadius: "0.25rem",
                      padding: "0.5rem",
                      minHeight: "100px",
                      whiteSpace: "pre-wrap",
                    }}
                    onInput={(e) => handleInputDescriptionChange({ target: { name: 'description', value: e.target.innerHTML } })}
                    dangerouslySetInnerHTML={{ __html: topic.description }}
                  ></div>
                </CCol>
              </CFormGroup>
              <CFormGroup row>
                <CCol>
                  <CLabel htmlFor="status" style={{ paddingRight: "20px" }}>Status</CLabel>
                  <CSwitch
                    id="status"
                    name="status"
                    color="success"
                    checked={topic.active}
                    onChange={handleStatusToggle}
                    shape="pill"
                    size="sm"
                  />
                  <CLabel style={{ paddingLeft: "20px" }}> {labelStatus} </CLabel>
                </CCol>
              </CFormGroup>
            </CForm>
          )}
        </CModalBody>
        {topic?.id && (
          <CCard style={{ height: "100hv" }}>
            <CCol style={{ textAlign: "end", paddingTop: "10px" }} md="12">
              <CButton
                color="info"
                onClick={() => handleCreate()}
              >
                <i className={"cil-plus"} /> Adicionar Itens
              </CButton>
            </CCol>
            <CCardBody>
              <TableSelectItens
                data={documentList}
                columns={fields}
                onSelectionChange={(_) => { }}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="100%"
              />
            </CCardBody>
          </CCard>
        )}
        <CModalFooter>
          {!topic?.id && (
            <CButton color="info" onClick={handleSubmit} disabled={isLoading}>
              <i className="cil-topic-plus"></i> Adicionar Tópico
            </CButton>
          )}
          {topic?.id && (
            <>
              <CButton color="info" onClick={handleEdit} disabled={isLoading}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>

      {isModalDocumentOpen && (
        <CreateTopicDocumentModal
          isOpen={isModalDocumentOpen}
          editDocument={selectedDocument}
          topicId={topic?.id}
          onClose={handleCloseDocumenteModal}
        />
      )}
    </>
  );
};

export default CreateTopicModal;
