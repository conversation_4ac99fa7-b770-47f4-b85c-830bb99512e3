import React, { useState, createContext, useContext, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import { POST_DATA, invalidTokenValidation } from "src/api";
import { getURI } from "src/config/apiConfig";

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [authToken, setAuthToken] = useState(localStorage.getItem("token"));
  const [authRefresh, setAuthRefresh] = useState(localStorage.getItem("authRefresh"));
  const [permissions, setPermissions] = useState(null);
  const [grupos, setGrupos] = useState(null);
  const [crms, setCrms] = useState(null);
  const [user, setUser] = useState(false);
  const [checkExecuted, setCheckExecuted] = useState(false);
  const [authTokenPermissionsAndGroups, setAuthTokenPermissionsAndGroups] = useState(localStorage.getItem("authTokenPermissionsAndGroups"));

  const isTokenValid = (token) => {
    try {
      const decoded = jwtDecode(token);
      const currentTime = Date.now() / 1000; // Tempo atual em segundos
      return decoded.exp > currentTime;
    } catch (error) {
      return false;
    }
  };

  const checkPermission = (name, perm, submodulo = null) => {
    const userRole = permissions ?? null;
    if (userRole) {
      if (userRole.isAdmin) return true;
      const foundModule = userRole.Modules.find(
        (module) => module.Module === name
      );
      if (!foundModule) return false;

      if (submodulo) {
        const foundSubmodule = foundModule.SubModules.find(
          (submodule) => submodule.Module === submodulo
        );
        return foundSubmodule ? foundSubmodule[perm] : false;
      }
      return foundModule[perm];
    }
    return false;
  };

  const checkGroup = (id) => {
    const userRole = permissions ?? null;
    const grupo = grupos ?? null;
    const crm = crms ?? null;
    if (userRole && userRole.isAdmin) return true;
    if (grupo && crm) {
      const filteGrupos = grupo.filter((item) => item.GroupDatacobId === id); // Filtra elementos undefined
      if (filteGrupos && filteGrupos.length === 0) return false;

      const gruposValidos = filteGrupos.filter((grupoItem) =>
        crm.includes(grupoItem.Crm)
      );
      if (gruposValidos && gruposValidos.length > 0) return true;
    }
    return false;
  };

  const addLogLoginUser = async (tipo, responseApi, userName) => {
    try {
      const data = { type: tipo, responseApi: responseApi, userName: userName };
      await POST_DATA(getURI("authLogUser"), data, true);
    } catch (error) {
      console.error("Erro:", error);
    }
  };

  const checkToken = async () => {
    setAuthToken(localStorage.getItem("token"));
    const validateToken = await invalidTokenValidation();
    if (!isTokenValid(authToken) || !validateToken) {
      const refreshToken = refreshToken || localStorage.getItem("refreshToken");
      if (!refreshToken || !validateToken) {
        alert("Atenção! Sua sessão foi encerrada! Será necessário realizar um novo login no sistema!");
        window.location.href = "/#/login";
        return false;
      }
      const request = { refreshToken: refreshToken };
      const isActiveTelephony = sessionStorage.getItem("activateTelephony")
        ? JSON.parse(sessionStorage.getItem("activateTelephony"))
        : false;
      const rawResponse = await POST_DATA(
        `${getURI("authRefresh")}?isPhone=${isActiveTelephony}`,
        request,
        true,
        true
      );

      if (rawResponse?.success) {
        loginAuthContext(rawResponse.data.accessToken);
        loginAuthRefresh(rawResponse.data.refreshToken);
      } else {
        window.location.href = "/#/login";
        return false;
      }
    }
    return true;
  };

  const inforPermissions = (permissions) => {
    return {
      create: !checkPermission(
        permissions.modulo,
        "Create",
        permissions.submodulo
      )
        ? `Você não tem permissão para Criar: Módulo: ${permissions.modulo}${permissions.submodulo
          ? " / Submodulo: " + permissions.submodulo
          : ""
        }, Flag: Criar `
        : "Criar",
      edit: !checkPermission(permissions.modulo, "Edit", permissions.submodulo)
        ? `Você não tem permissão para Editar:  Módulo: ${permissions.modulo}${permissions.submodulo
          ? " / Submodulo: " + permissions.submodulo
          : ""
        }, Flag: Editar `
        : "Editar",
      delete: !checkPermission(
        permissions.modulo,
        "Delete",
        permissions.submodulo
      )
        ? `Você não tem permissão para Deletar:  Módulo: ${permissions.modulo}${permissions.submodulo
          ? " / Submodulo: " + permissions.submodulo
          : ""
        }, Flag: Deletar `
        : "Deletar",
      view: !checkPermission(permissions.modulo, "View", permissions.submodulo)
        ? `Você não tem permissão para Visualizar:  Módulo: ${permissions.modulo
        }${permissions.submodulo
          ? " / Submodulo: " + permissions.submodulo
          : ""
        }, Flag: Ver `
        : "Ver",
    };
  };

  const loginAuthContext = (newToken) => {
    if (isTokenValid(newToken)) {
      setAuthToken(newToken);
      localStorage.setItem("token", newToken);
    } else {
      setAuthToken(null);
      localStorage.removeItem("token");
      localStorage.removeItem("authTokenPermissionsAndGroups");
      setAuthTokenPermissionsAndGroups(null);
    }
  };

  const loginAuthRefresh = (newAuthRefresh) => {
    if (newAuthRefresh) {
      setAuthRefresh(newAuthRefresh);
      localStorage.setItem("refreshToken", newAuthRefresh);
    } else {
      setAuthRefresh(null);
      localStorage.removeItem("refreshToken");
    }
  };

  useEffect(() => {
    if (
      authTokenPermissionsAndGroups !== null &&
      authTokenPermissionsAndGroups !== undefined &&
      authTokenPermissionsAndGroups !== ""
    ) {
      setConfigUser(authTokenPermissionsAndGroups);
    }
  }, [authTokenPermissionsAndGroups]);

  const setConfigUser = async (tokenPermissions) => {
    if (tokenPermissions) {
      const decoded = jwtDecode(tokenPermissions);
      const currentTime = Date.now() / 1000;
      if (decoded.exp > currentTime) {
        const permissionsV =
          decoded && decoded.Permissions && decoded.Permissions !== "null"
            ? JSON.parse(decoded.Permissions)
            : {};
        const grupos =
          decoded && decoded.Groups ? JSON.parse(decoded.Groups) : [];
        const crms = decoded && decoded.Crms ? JSON.parse(decoded.Crms) : [];
        permissionsV.isAdmin =
          decoded && decoded.role && decoded.role === "Admin";
        setPermissions(permissionsV);
        setGrupos(grupos);
        setCrms(crms);
      }
    }
  };

  const handler = () => {
    setCheckExecuted(true);
    checkToken();
  };

  useEffect(() => {
    if (checkExecuted) return;
    window.addEventListener("checkTokenRequest", handler);
    return () => {
      window.removeEventListener("checkTokenRequest", handler);
      setCheckExecuted(false);
    };
  }, [handler]);

  const logoutAuthContext = async () => {
    await addLogLoginUser("logout", "Usuario Deslogado", user.username);
    setPermissions(null);
    setGrupos(null);
    setCrms(null);
    localStorage.clear();
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        authToken,
        loginAuthContext,
        loginAuthRefresh,
        permissions,
        logoutAuthContext,
        isTokenValid,
        checkToken,
        checkPermission,
        checkGroup,
        inforPermissions,
        setConfigUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
