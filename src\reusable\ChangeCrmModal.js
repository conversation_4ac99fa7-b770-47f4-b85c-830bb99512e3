import React, { useState, useEffect } from "react";
import { CModal, CModalBody, CModalHeader, CModalFooter, CRow, CCol, CButton, CModalTitle, CCardBody, CForm, CFormGroup } from "@coreui/react";
import Select from "react-select";
// eslint-disable-next-line no-unused-vars
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import EditUserCrmAuthModal from "src/views/configuracoes/gerenciarUsuarios/Modal/EditUserCrmAuthModal.tsx";

const ChangeCrmModal = ({ onClose, onChangeCrm }) => {
  const [crm, setCrm] = useState(null);
  const [crmList, setCrmList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [close, setClose] = useState(false);
  const [editUserCrmAuthModal, setEditUserCrmAuthModal] = useState(false);

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const handleConfirm = () => {
    if (crm !== null) {
      updateConnection();
    }
  };

  const handleSelectCrm = (input) => {
    setCrm(input);
  };

  const getAllDatacobs = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const postUpdateConnection = async (payload, endpoint = "postUserUpdateConection") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const postLoginDatacob = async (payload, endpoint = "postDatacobLogin") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await POST_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const updateConnection = () => {
    setLoading(true);
    const payload = {
      activeConnection: crm.datacobNumber,
      userId: user.id,
    };
    postUpdateConnection(payload).then((data) => {
      if (data?.success === false) {
        setError(data.message);
        setClose(false);
      } else {
        if (user?.isAdmin === false) {
          postLoginDatacob().then((data) => {
            if (data.success === false) {
              setError(data.message);
              setEditUserCrmAuthModal(true);
              setClose(false);
            } else {
              setClose(true);
            }
          }).catch((err) => {
            console.log(err);
            setLoading(false);
          }).finally(() => {
            setLoading(false);
          });
        } else {
          setClose(true);
        }
      }
    }).catch((err) => {
      console.log(err);
    }).finally(() => { });
  };

  const getCrms = () => {
    setLoading(true);
    getAllDatacobs(null, "getDatacobs").then((data) => {
      if (data) {
        const uniqueDatacob = [...new Set(data.map((item) => item))];
        const optionsDatacob = [
          ...uniqueDatacob.map((x) => ({
            datacobNumber: x.datacobNumber,
            datacobName: "Datacob " + x.datacobName,
          })),
        ];
        setCrmList(optionsDatacob);
      }
    }).catch((err) => {
      console.log(err);
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    getCrms();
  }, []);

  useEffect(() => {
    if (close === true) {
      onClose(crm?.datacobNumber);
    }

    if (error !== "" && close === false) {
      toast.info(error);
      setError("");
    }
  }, [close, error]);

  return (
    <>
      <CModal
        show={true}
        onClose={onClose}
        closeOnBackdrop={false}
        className="custom-modal"
      >
        <CModalHeader>
          <CModalTitle>
            Identifiquei que você possui acesso a mais de um Datacob (CRM), por
            favor, escolha qual CRM deseja usar para trabalhar
          </CModalTitle>
        </CModalHeader>
        <CModalBody>
          <CRow>
            <CCol>
              <CCardBody className="py-1">
                {loading ? (
                  <CardLoading />
                ) : (
                  <>
                    <CForm>
                      <CFormGroup>
                        <Select
                          className="mb-2"
                          value={crm}
                          onChange={handleSelectCrm}
                          options={crmList}
                          getOptionValue={(option) => option.datacobNumber}
                          getOptionLabel={(option) => option.datacobName}
                          placeholder={"Selecione..."}
                        />
                      </CFormGroup>
                    </CForm>
                  </>
                )}
              </CCardBody>
            </CCol>
          </CRow>
        </CModalBody>
        <CModalFooter>
          <CButton color="info" onClick={handleConfirm}>
            Confirmar
          </CButton>
        </CModalFooter>
      </CModal>
      {editUserCrmAuthModal && (
        <EditUserCrmAuthModal
          editUser={user}
          onClose={() => {
            setEditUserCrmAuthModal(false);
          }}
        />
      )}
    </>
  );
};

export default ChangeCrmModal;
