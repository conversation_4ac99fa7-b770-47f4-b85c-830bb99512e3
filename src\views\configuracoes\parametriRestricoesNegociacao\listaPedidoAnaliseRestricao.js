import { <PERSON>utton, <PERSON>ard, CCardBody, CCol, CDataTable, CForm, CFormGroup, CRow, CLabel, CModalBody } from "@coreui/react";
import Select from "react-select";
import React, { useEffect, useState } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { toast } from "react-toastify";
import { MountURI, GET_DATA, PUT_DATA, token } from "src/api";
import { getURI } from "src/config/apiConfig";
import CardLoading from "src/reusable/CardLoading";
import ConfirmModal from "src/reusable/ConfirmModal";
import { formatDateTime } from "src/reusable/helpers";
import { showMenuAprovacaoJuridica } from "./../../../containers/AprovacaoJuridica";
import { useMyContext } from "src/reusable/DataContext";

const ParametriRestricoesNegociacao = () => {

    const [data, setData] = useState([]);
    const context = useMyContext();
    const [isLoading, setIsLoading] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const [statusAtual, setStatusAtual] = useState(null);

    const [selectedDatacob, setSelectedDatacob] = useState(null);
    const [selectedGrupo, setSelectedGrupo] = useState(null);
    const [selectedCliente, setSelectedCliente] = useState(null);
    const [groupOptions, setGroupOptions] = useState([]);
    const [datacobOptions, setDatacobOptions] = useState([]);
    const [clientsOptions, setClientsOptions] = useState([]);
    const [hasMultipleConnections, setHasMultipleConnections] = useState(false);


    const handleSelectDatacob = (selectedOption) => {
        setSelectedDatacob(selectedOption);
    };

    const handleSelectGrupo = (selectedOption) => {
        setSelectedGrupo(selectedOption);
    };

    const handleSelectCliente = (selectedOption) => {
        setSelectedCliente(selectedOption);
    };

    const userProfile = localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")) : "";

    const columns = [
        {
            key: "user",
            label: "Usuário",
        },
        {
            key: "financed",
            label: "Financiado",
        },
        {
            key: "fase",
            label: "Fase",
        },
        {
            key: "created_at",
            label: "Data Solicitação",
            // formatter: (item) => formatDateTime(item),
        },
        {
            key: "folders",
            label: "Pastas",
            //formatter: (item) => formatPastas(item),
        },
        {
            key: "status",
            label: "Status",
        },
        {
            key: "actions",
            label: "Ações",
            //formatterByObject: (item) => renderActionButton(item),
        }
    ];

    const formatPastas = (item) => {
        if (item !== '') {
            let listPastas = JSON.parse(item);
            listPastas = listPastas.join(' / ');
            return (<td>{listPastas}</td>);
        }
        return (<td></td>);

    }

    async function getListaAnalisesRestricoes() {
        setData([]);
        setIsLoading(true);
        let params = null;
        if ((selectedGrupo?.id ?? 0) > 0 || (selectedCliente?.id ?? 0) > 0) {
            params = {
                ActiveConnection: selectedDatacob.datacobNumber,
            }

            if ((selectedGrupo?.id ?? 0) > 0) params = { ...params, id_Grupo: selectedGrupo.id};
            if ((selectedCliente?.id ?? 0) > 0) params = { ...params, id_Cliente: selectedCliente.id};
        }

        const listaAnalisesRestricoes = await GET_DATA(getURI("negociacaoAnalisesRestricaoListar"), params, true);

        if (listaAnalisesRestricoes) {
            setData(listaAnalisesRestricoes);
        }
        setIsLoading(false);
        return;
    }

    const financiadoData = localStorage.getItem("financiadoData")
        ? JSON.parse(localStorage.getItem("financiadoData"))
        : null;


    const history = useHistory();

    useEffect(async () => {
        if (!await showMenuAprovacaoJuridica()) {
            history.push('/telaprincipal');
        }
        await getListaAnalisesRestricoes();
    }, []);

    const updateStatus = async (confirmation) => {
        if (confirmation) {
            const data = { status: statusAtual };
            const update = await PUT_DATA(getURI("negociacaoAnalisesRestricaoUpdateStatus") + "/" + selectedItem.id, data, true);
            if (update.success) {
                toast.success(update.message);
                if (financiadoData != null)
                    context.updateData(financiadoData)
                await getListaAnalisesRestricoes();
            } else {
                alert(update.message);
            }
        }
    }

    const handleModalClose = (confirmation) => {
        setShowConfirmModal(false);
        updateStatus(confirmation);
    };

    const handleConfirmModal = (item, newStatus) => {
        setSelectedItem(item);
        setStatusAtual(newStatus)
        setShowConfirmModal(true);
    };

    const getAllDatacobs = async (payload, endpoint) => {
        return new Promise(async (resolve, reject) => {
            try {
                const response = await GET_DATA(getURI(endpoint), payload, true);
                resolve(response);
            } catch (error) {
                reject(error);
            }
        });
    };

    const updateOptionsAdm = () => {
        setIsLoading(true);
        getAllDatacobs(null, "getDatacobs").then((data) => {
            if (data) {
                const uniqueDatacob = [...new Set(data.map((item) => item))];
                const optionsDatacob = [
                    ...uniqueDatacob.map((x) => ({
                        datacobNumber: x.datacobNumber,
                        datacobName: "Datacob " + x.datacobName,
                    })),
                ];
                setDatacobOptions(optionsDatacob);
            }
        }).catch((err) => {
            console.log(err);
        }).finally(() => {
            setIsLoading(false);
        });
    };

    async function checkDatacobSelection() {
        if (userProfile && userProfile.isAdmin === true) {
            updateOptionsAdm();
            setHasMultipleConnections(true);
        } else if (userProfile && userProfile.datacobs.length > 1) {
            const uniqueDatacob = [
                ...new Set(userProfile.datacobs.map((item) => item)),
            ];
            const optionsDatacob = [
                ...uniqueDatacob.map((x) => ({
                    datacobNumber: x.datacobNumber,
                    datacobName: "Datacob " + x.datacobName,
                })),
            ];
            setDatacobOptions(optionsDatacob);
            setHasMultipleConnections(true);
        } else if (userProfile.datacobs.length === 0) {
            setSelectedDatacob({ datacobNumber: userProfile.activeConnection });
        } else {
            setSelectedDatacob(userProfile.datacobs[0]);
        }
    }

    const updateGrupoOptions = () => {
        const payload = {
            ActiveConnection: selectedDatacob.datacobNumber,
        };
        getGroups(payload, "getDatacobGroups").then((data) => {
            if (data) {
                const groupList = data.map((group) => ({
                    id: group.id_Grupo,
                    name: group.descricao,
                }));

                const allOption = { id: "", name: "Todos" };
                const optionsGroup = [allOption, ...groupList];
                setGroupOptions(optionsGroup);
            }
        }).catch((err) => {
            console.log(err);
        });
    };
    const getGroups = async (payload, endpoint) => {
        return new Promise(async (resolve, reject) => {
            try {
                const response = await GET_DATA(getURI(endpoint), payload, true);
                resolve(response);
            } catch (error) {
                reject(error);
            }
        });
    };

    const getClients = async () => {
        const data = {
            ActiveConnection: selectedDatacob.datacobNumber,
            GroupId: selectedGrupo.id,
        };
        const url = MountURI("Datacob/Clients", data);

        try {
            const response = await fetch(url, {
                headers: {
                    Authorization: `Bearer ${token()}`,
                },
            });

            if (response.ok) {
                const responseData = await response.json();
                const clientOptions = responseData.data.map((x) => ({
                    cnpj: x.cnpj,
                    id: x.id_Cliente,
                    name: x.nome_Res,
                }));
                setClientsOptions(clientOptions);
            } else {
                console.error("Erro:", response?.statusText);
            }
        } catch (error) {
            console.error("Erro buscando clientes:", error);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            await checkDatacobSelection();
        };
        fetchData();
    }, []);

    useEffect(() => {
        if (selectedDatacob) {
            updateGrupoOptions();
        }
    }, [selectedDatacob]);

    useEffect(() => {
        if (selectedGrupo) {
            getClients();
        }
    }, [selectedGrupo]);

    const renderActionButton = (item) => {
        if (item.status == 'Pendente') {
            return (
                <>
                    <CButton
                        color="success"
                        onClick={() => handleConfirmModal(item, 'Aprovado')}
                        className="mr-2"
                    >
                        Aprovado
                    </CButton>
                    <CButton
                        color="danger"
                        onClick={() => handleConfirmModal(item, 'Rejeitado')}
                        className="mr-2"
                    >
                        Rejeitado
                    </CButton>
                </>
            )
        } else {
            return (<></>)
        }
    }

    return (
        <div>
            <h3>Lista para aprovação de solicitações:</h3>
            <div style={{ color: "gray" }}>
                <CModalBody>
                    <CRow>
                        {hasMultipleConnections && (
                            <CCol md="3">
                                <CFormGroup>
                                    <CLabel>CRM</CLabel>
                                    <Select
                                        value={selectedDatacob}
                                        onChange={handleSelectDatacob}
                                        options={datacobOptions}
                                        getOptionValue={(option) => option.datacobNumber}
                                        getOptionLabel={(option) => option.datacobName}
                                        placeholder={"Selecione..."}
                                        isClearable={true}
                                    />
                                </CFormGroup>
                            </CCol>
                        )}
                        <CCol md="3">
                            <CFormGroup>
                                <CLabel>Grupo</CLabel>
                                <Select
                                    placeholder="Selecione"
                                    value={selectedGrupo}
                                    onChange={handleSelectGrupo}
                                    options={groupOptions}
                                    getOptionValue={(option) => option.id}
                                    getOptionLabel={(option) => option.name}
                                    isDisabled={!selectedDatacob}
                                    isClearable={true}
                                />
                            </CFormGroup>
                        </CCol>
                        <CCol md="3">
                            <CFormGroup>
                                <CLabel>Cliente</CLabel>
                                <Select
                                    placeholder="Selecione"
                                    value={selectedCliente}
                                    onChange={handleSelectCliente}
                                    options={clientsOptions}
                                    getOptionValue={(option) => option.id}
                                    getOptionLabel={(option) => option.name}
                                    isDisabled={!selectedGrupo}
                                    isClearable={true}
                                />
                            </CFormGroup>
                        </CCol>
                        <CCol md="1">
                            <CButton
                                style={{ marginTop: '31px' }}
                                className="btn-custom-outline-green"
                                icon="cil-search"
                                color={"green"}
                                onClick={() => { getListaAnalisesRestricoes() }}
                            > Pesquisar
                            </CButton>
                        </CCol>
                    </CRow>
                </CModalBody>
            </div>
            <div>
                <CCard >
                    <CCardBody>
                        <CForm>
                            <CRow>
                                <CCol xs>
                                    <CRow>
                                        {isLoading ? (
                                            <CardLoading />
                                        ) : (

                                            <CDataTable
                                                items={data}
                                                fields={columns}
                                                scopedSlots={{
                                                    created_at: (item) => (<td>{formatDateTime(item.created_at)}</td>),
                                                    folders: (item) => formatPastas(item.folders),
                                                    actions: (item) => {
                                                        return (<td>{renderActionButton(item)}</td>)
                                                    }
                                                }}
                                                pagination
                                                sorter
                                                columnFilter
                                                itemsPerPage={10}
                                                heightParam="600px"
                                            />
                                        )}

                                        <ConfirmModal
                                            isOpen={showConfirmModal}
                                            onClose={handleModalClose}
                                            texto={"Tem certeza que deseja aplicar essa ação?"}
                                        />
                                    </CRow>
                                </CCol>
                            </CRow>

                        </CForm>
                    </CCardBody>
                </CCard>
            </div>
        </div>

    );
};



export default ParametriRestricoesNegociacao;