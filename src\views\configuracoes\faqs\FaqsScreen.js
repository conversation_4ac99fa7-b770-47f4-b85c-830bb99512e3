import React, { useEffect, useState } from "react";
import { CLabel, CCol, CInput, CCard, CCardBody, CButton, CBadge } from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { token } from "src/api";

const FaqsScreen = () => {
    const [topicsList, setTopicsList] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [activeSlide, setActiveSlide] = useState(null);
    const [documentUrls, setDocumentUrls] = useState({});

    const filteredTopics = topicsList.filter((faq) =>
        faq.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    useEffect(() => {
        const fetchData = async () => {
            try {
                await getTopics("");
            } catch (error) {
                console.error("Erro buscando tópicos:", error);
            }
        };
        fetchData();
    }, []);

    const getTopics = async (search) => {
        try {
            const response = await fetch(`${getURI()}/Topic/AllTopics?take=10&search=${search}`, {
                headers: { Authorization: `Bearer ${token()}` }
            });

            if (response.ok) {
                const data = await response.json();
                setTopicsList(data.data);
            } else {
                console.error("Erro:", response?.statusText);
            }
        } catch (error) {
            console.error("Erro buscando usuários:", error);
        }
    };

    const getDocuments = async (url, typeFile) => {
        try {
            const response = await fetch(url, {
                headers: { Authorization: `Bearer ${token()}` },
            });

            if (response.ok) {
                const blob = await response.blob();
                const base64data = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result.replace("data:text/html;", `data:${typeFile};`));
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
                return base64data;
            } else {
                console.error("Erro ao buscar documento:", response.status, response.statusText);
                return null;
            }
        } catch (error) {
            console.error("Erro ao buscar documento:", error);
            return null;
        }
    };

    const getTypeFile = (documentTypeId) => {
        if (documentTypeId === "Pdf") return "application/pdf";
        if (documentTypeId === "Video") return "video/mpeg";
        if (documentTypeId === "Image") return "image/jpeg";
    }

    const handleSlideToggle = async (topicId) => {
        if (activeSlide === topicId) {
            setActiveSlide(null);
            return;
        }

        setActiveSlide(topicId);

        const topic = filteredTopics.find((t) => t.id === topicId && t.active);
        if (topic) {
            const urls = { ...documentUrls };
            for (const doc of topic.topicDocuments) {
                if (doc.active && doc.filePath && !urls[doc.id]) {
                    urls[doc.id] = await getDocuments(doc.filePath, getTypeFile(doc.documentTypeId)); // Chama a API para carregar o documento.
                }
            }
            setDocumentUrls(urls);
        }
    };

    return (
        <div className="faq-container">
            <CCol md="12" className="mb-3">
                <CLabel htmlFor="search">Pesquisar FAQ</CLabel>
                <CInput
                    id="search"
                    placeholder="Digite para pesquisar tópicos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </CCol>

            {filteredTopics.sort((a, b) => a.id - b.id).map((topic) => (
                <CCard key={topic.id} className="mb-3">
                    <CCardBody>
                        <div className="d-flex justify-content-between align-items-center">
                            <h5>{topic.name}</h5>
                        </div>
                        <p dangerouslySetInnerHTML={{ __html: topic.description }}></p>

                        {topic.topicDocuments.length > 0 ? (
                            <CButton
                                color="primary"
                                onClick={() => handleSlideToggle(topic.id)}
                            >
                                {activeSlide === topic.id ? "Ocultar Documentos" : "Exibir Documentos"}
                            </CButton>
                        ) : (
                            <CBadge color="secondary">Sem documentos</CBadge>
                        )}

                        {activeSlide === topic.id && (
                            <div className="document-slider mt-3">
                                {topic.topicDocuments.filter(t => t.active).sort((a, b) => a.id - b.id).map((doc, index) => (
                                    <CCard key={doc.id} className="mb-2">
                                        <CCardBody>
                                            <h5>{doc.title}</h5>
                                            <p dangerouslySetInnerHTML={{ __html: doc.description }}></p>
                                            {doc.documentTypeId === "Nofile" && (
                                                <CBadge color="danger">Sem Arquivo</CBadge>
                                            )}
                                            {doc.documentTypeId === "Pdf" && documentUrls[doc.id] && (
                                                <iframe
                                                    src={documentUrls[doc.id]}
                                                    title={doc.title}
                                                    width="100%"
                                                    height="400"
                                                ></iframe>
                                            )}
                                            {doc.documentTypeId === "Doc" && documentUrls[doc.id] && (
                                                <iframe
                                                    src={documentUrls[doc.id]}
                                                    title={doc.title}
                                                    width="100%"
                                                    height="400"
                                                ></iframe>
                                            )}
                                            {doc.documentTypeId === "Video" && documentUrls[doc.id] && (
                                                <video controls width="100%">
                                                    <source src={documentUrls[doc.id]} type="video/mp4" />
                                                </video>
                                            )}
                                            {doc.documentTypeId === "Image" && documentUrls[doc.id] && (
                                                <img
                                                    src={documentUrls[doc.id]}
                                                    alt={doc.title}
                                                    width="80%"
                                                />
                                            )}
                                        </CCardBody>
                                    </CCard>
                                ))}
                            </div>
                        )}
                    </CCardBody>
                </CCard>
            ))}
        </div>
    );
};

export default FaqsScreen;
