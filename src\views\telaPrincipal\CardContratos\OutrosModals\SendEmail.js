import { useState, useRef, useEffect } from "react";
import Select from "react-select";
import { CButton, CModal, CModalBody, CModalHeader, CModalFooter, CFormGroup, CForm, CRow, CCol, CLabel, CInput } from "@coreui/react";
import { isEmailValid } from "src/reusable/helpers";
import PropTypes from "prop-types";
import { toast } from "react-toastify";
import TableSelectItens from "src/reusable/TableSelectItens";
import { getURI } from "src/config/apiConfig";
import { DELETE_DATA, token } from "src/api";
import CreatableSelect from 'react-select/creatable';

const SendEmail = ({ contract, user, editEmail, isOpen, onClose }) => {

  const [email, setEmail] = useState({
    id: 0,
    contractId: 0,
    groupId: 0,
    crmId: 0,
    userId: 0,
    contract: "",
    subject: "",
    destination: "",
    fromCopy: "",
    content: "",
    attachments: [],
    sendDate: "",
    status: 0,
    isBodyHtml: false,
    active: true
  });

  const clientData = localStorage.getItem("clientData") ? JSON.parse(localStorage.getItem("clientData")) : "";
  const [isValid, setIsValid] = useState({ destination: true, fromCopy: true });
  const [emailList, setEmailList] = useState([]);
  const editorRef = useRef(null);

  const listaStatus = [
    { value: 0, label: "Criado" },
    { value: 1, label: "Enviado" },
    { value: 2, label: "Re-enviado" },
    { value: 3, label: "Falha no envio" },
    { value: 4, label: "Cancelado" },
  ];

  const handleStatusChange = (selectedOption) => {
    setEmail((prevState) => ({ ...prevState, status: selectedOption.value, }));
  };

  const handleEmailChange = (selectedOption) => {
    setEmail((prevState) => ({ ...prevState, destination: selectedOption?.value }));
    handleValidationBlur({ target: { name: 'destination', value: selectedOption?.value ?? '' } });
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setEmail((prevState) => ({ ...prevState, [name]: value }));
  };

  const handleValidationBlur = (event) => {
    const { name, value } = event.target;
    const valid = value?.trim() === "" || isEmailValid(value?.trim());
    setIsValid((prev) => ({ ...prev, [name]: valid }));
  };

  function resetModal() {
    setEmail({
      id: 0,
      contractId: contract.id_Contrato,
      groupId: contract.id_Grupo,
      crmId: contract.coddatacob === "GVC" ? 2 : 1,
      userId: user.id,
      contract: contract.numero_Contrato,
      subject: "",
      destination: "",
      fromCopy: "",
      content: "",
      attachments: [],
      sendDate: new Date(),
      status: 0,
      isBodyHtml: true,
      active: true
    });
  }

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const SaveEmail = async (email) => {
    const url = `${getURI()}/SendFromEmail`;

    if (email.id > 0) {
      await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}`, },
        body: JSON.stringify({
          ...email,
          id: email.id,
        }),
      });

    } else {
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token()}` },
        body: JSON.stringify(email),
      });
      setEmail(response);
    }
  }

  const handleSave = (event) => {
    event.preventDefault();

    const requiredFields = [{ name: "subject", displayName: "Assunto" }, { name: "destination", displayName: "Destinatário" }, { name: "content", displayName: "Texto" }];

    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = email[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      setIsValid({});
      return;
    }

    SaveEmail(email);
    resetModal();
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      let isMounted = true;
      resetModal();
      setEmailList([
        ...clientData?.emails?.map((item) => {
          return { endereco_Email: item.endereco_Email };
        }) ?? []
      ]);

      if (editEmail?.id && isMounted) {
        setEmail(editEmail);
      }
      return () => { isMounted = false; };
    }
  }, [isOpen, editEmail]);

  useEffect(() => {
    if (isOpen && editorRef.current) {
      editorRef.current.innerHTML = email.content;
    }
  }, [isOpen, editorRef.current]);

  const emailOptions = [
    ...emailList.map((x) => ({
      value: x.endereco_Email,
      label: x.endereco_Email,
    })),
  ];

  const handleCreateEmail = (inputValue) => {
    handleValidationBlur({ target: { name: "destination", value: inputValue } });
    if (isValid.destination) {
      const newOption = { value: inputValue, label: inputValue };
      emailOptions.push(newOption);
      emailList.push({ endereco_Email: inputValue });
      handleEmailChange(newOption);
    } else {
      toast.info("Email inválido não pode ser adicionado.");
      setIsValid({});
    }
  };

  const applyFormat = (command, value = null) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
    }
  };

  const handleFilePathChange = async (event) => {
    const file = event.target.files[0];
    const allowedTypes = ["application/pdf", "application/msword", "video/mp4", "image/jpeg"];
    if (!allowedTypes.includes(file.type)) {
      toast.info("Tipo de arquivo não permitido.");
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      toast.info("Arquivo excede o tamanho máximo permitido de 5MB.");
      return;
    }
    await handleUploadFile(file.name, file);
  };

  const handleExcluirArquivo = async (item) => {
    var attachments = email.attachments.filter((attachment) => attachment.file !== item.file);
    setEmail((prevEmail) => ({ ...prevEmail, attachments: attachments }));

    if (!item?.id) return;
    const deleteSuccess = await DELETE_DATA("SendFromEmailAttachment/" + item?.id);
    if (deleteSuccess?.success) {
      toast.success("Anexo removido com sucesso.");
    } else {
      toast.error("Erro ao remover anexo.");
    }
  };

  const handleUploadFile = async (fileName, file) => {
    try {
      const url = `${getURI()}/File/upload?topicId=0`;

      const formData = new FormData();
      formData.append("formFile", file);
      formData.append("fileName", fileName);

      const response = await fetch(url, {
        method: "POST",
        headers: { Authorization: `Bearer ${token()}` },
        body: formData,
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Arquivo carregado com sucesso");
          var attachments = email.attachments || [];
          attachments.push({ fileType: 1, fileName: file.name, file: `${getURI()}/File/${status.filePath}` });

          setEmail((prevEmail) => ({ ...prevEmail, attachments: attachments }));
        } else {
          toast.warning("Falha ao carregar Arquivo");
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro ao carregar Arquivo", error);
    }
  };

  const IconButton = ({ icon, color, titulo, onClick }) => {
    return (
      <CButton
        title={titulo}
        className="mr-1"
        style={{ border: "solid 1px", borderColor: color, color: color, padding: "2px 4px" }}
        onClick={onClick}
      >
        <i className={icon} />
      </CButton>
    );
  };

  const renderAction = (item) => {
    return (
      <div style={{ display: "flex" }}>
        <IconButton
          icon={"cil-trash"}
          color="danger"
          onClick={() => handleExcluirArquivo(item)}
          size="sm"
          className="mr-2"
          title={"Excluir Arquivo"}
          disabled={email.status !== 0}
        />
      </div>
    );
  };

  const tableColumns = [
    { key: "fileName", label: "Arquivo" },
    {
      key: "action",
      label: "Alterar",
      formatterByObject: (item) => renderAction(item),
    },
  ];

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Envio de Email</CModalHeader>
      <CModalBody>
        <CForm>
          <CFormGroup>
            <CRow>
              <CCol>
                <CLabel>Destinatário</CLabel>
                <CreatableSelect
                  id="destination"
                  name="destination"
                  isClearable
                  placeholder="Selecione ou digite um e-mail"
                  onChange={handleEmailChange}
                  onCreateOption={handleCreateEmail}
                  options={emailOptions}
                  value={emailOptions.find(opt => opt.value === email.destination) || null}
                  style={{ borderColor: isValid.destination ? "initial" : "red" }}
                />{" "}
                {!isValid.destination && <p style={{ color: "red" }}>Email inválido</p>}
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel>Com Cópia para</CLabel>
                <CInput
                  id="fromCopy"
                  name="fromCopy"
                  type="text"
                  value={email.fromCopy}
                  onChange={handleInputChange}
                  onBlur={handleValidationBlur}
                  style={{ borderColor: isValid.fromCopy ? "initial" : "red" }}
                />{" "}
                {!isValid.fromCopy && <p style={{ color: "red" }}>Email inválido</p>}
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel>Assunto</CLabel>
                <CInput
                  id="subject"
                  name="subject"
                  type="text"
                  value={email.subject}
                  onChange={handleInputChange}
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CLabel htmlFor="description">Texto</CLabel>
                <div className="mb-2 d-flex gap-2">
                  <CButton size="sm" color="primary" onClick={() => applyFormat("bold")}>
                    <i className="cil-bold"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("italic")}>
                    <i className="cil-italic"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("underline")}>
                    <i className="cil-underline"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("justifyLeft")}>
                    <i className="cil-align-left"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("justifyCenter")}>
                    <i className="cil-align-center"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("justifyRight")}>
                    <i className="cil-align-right"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("justifyFull")}>
                    <i className="cil-notes"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "5")}>
                    <i className="cil-text-square"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("fontSize", "3")}>
                    <i className="cil-text-size"></i>
                  </CButton>
                  <CButton size="sm" color="primary" onClick={() => applyFormat("insertParagraph")}>
                    <i className="cil-paragraph"></i>
                  </CButton>
                </div>
                <div
                  ref={editorRef}
                  contentEditable
                  id="content"
                  name="content"
                  style={{
                    border: "1px solid #ced4da",
                    borderRadius: "0.25rem",
                    padding: "0.5rem",
                    minHeight: "100px",
                    whiteSpace: "pre-wrap",
                  }}
                  onInput={(e) => handleInputChange({ target: { name: 'content', value: e.target.innerHTML } })}
                ></div>
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol md="6">
                <CLabel>Status</CLabel>
                <Select
                  id="status"
                  name="status"
                  value={listaStatus.find((option) => option.value === email.status)}
                  onChange={handleStatusChange}
                  options={listaStatus}
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              <CCol>
                <CLabel htmlFor="filePath">Carregar um arquivo</CLabel>
                <CInput
                  type="file"
                  id="filePath"
                  name="filePath"
                  accept=".doc,.pdf,.mp4,.jpg"
                  onChange={handleFilePathChange}
                  required
                />
              </CCol>
            </CRow>
          </CFormGroup>
          <CFormGroup>
            <CRow>
              {email.attachments.length != 0 && (
                <TableSelectItens
                  data={email.attachments}
                  columns={tableColumns}
                  onSelectionChange={(_) => { }}
                  defaultSelectedKeys={[]}
                  selectable={false}
                />
              )}
            </CRow>
          </CFormGroup>
        </CForm>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Cancelar
        </CButton>
        <CButton color="primary" onClick={handleSave} disabled={!isValid.fromCopy || !isValid.destination || !email?.destination}>
          Enviar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

SendEmail.propTypes = {
  contract: PropTypes.object.isRequired,
  user: PropTypes.object.isRequired,
  editEmail: PropTypes.object,
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
};

export default SendEmail;
