import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CTextarea,
} from "@coreui/react";
import { getApi, putApi } from "src/reusable/functions";

import "react-quill/dist/quill.snow.css";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, PUT_DATA } from "src/api";

import { Editor } from "@tinymce/tinymce-react";
import LoadingComponent from "src/reusable/Loading";

interface TipoTermo {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
  conteudo: ConteudoTermoType[];
}

interface ConteudoTermoType {
  nome: string;
  crm: string;
  grupoId: string;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ConteudoTermosModal = ({ isOpen, onClose }: Props) => {
  const [tiposTermos, setTiposTermos] = useState<TipoTermo[]>([]);

  const [selectedTermo, setSelectedTermo] = useState<TipoTermo | null>(null);
  const [editingTermo, setEditingTermo] = useState<TipoTermo | null>(null);
  const [expandedTermos, setExpandedTermos] = useState<Set<string>>(new Set());

  const [loading, setLoading] = useState(false);
  const [loadingConteudo, setLoadingConteudo] = useState(false);
  const [conteudoTermoSelected, setConteudoTermoSelected] = useState(null);

  const [value, setValue] = useState("");

  const handleSelectTermo = (termo: TipoTermo) => {
    setSelectedTermo(termo);
    setEditingTermo({ ...termo });
    getConteudoTermoByTipeTermo(termo.id).then((response) => {
      setValue(response[0]?.html);
      setConteudoTermoSelected(response[0]);
    });
  };

  const toggleExpandTermo = (termoId: string) => {
    setExpandedTermos((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(termoId)) {
        newSet.delete(termoId);
      } else {
        newSet.add(termoId);
      }
      return newSet;
    });
  };

  const handleSave = async () => {
    if (!editingTermo) return;

    setTiposTermos((prev) =>
      prev.map((termo) => (termo.id === editingTermo.id ? editingTermo : termo))
    );
    setSelectedTermo(editingTermo);
    await updateConteudoTermo();
  };

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo()]);
    await getTipoTermo();
    setLoading(false);
  }, []);

  const getTipoTermo = async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTiposTermos(response);
      } else {
        setTiposTermos([]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getConteudoTermoByTipeTermo = async (id: string) => {
    try {
      setLoadingConteudo(true);
      const response = await GET_DATA(
        getURI("getConteudoTermoByTipeTermo"),
        null,
        true,
        true,
        `/${id}`
      );
      setLoadingConteudo(false);
      return response;
    } catch (error) {
      console.error(error);
    }
    setLoadingConteudo(false);
  };

  const updateConteudoTermo = async () => {
    if (!conteudoTermoSelected) return;
    const formData = new FormData();
    formData.append("id", conteudoTermoSelected?.id);
    formData.append("html", value);
    formData.append("cabecalhoImg", conteudoTermoSelected?.cabecalhoImg ?? "");
    formData.append("rodapeImg", conteudoTermoSelected?.rodapeImg ?? "");
    formData.append("grupoId", conteudoTermoSelected?.grupoId || "");
    formData.append("crm", conteudoTermoSelected?.crm ?? "");
    //const res = await PUT_DATA(getURI("putConteudoTermo"), formData, true);

    const result = await fetch(getURI("putConteudoTermo"), {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${window.localStorage.getItem("token")}`,
      },
      body: formData,
    });
    const res = await result.json();

    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {};
  }, [asyncLoadFunc]);

  return (
    <CModal
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal modal-xxl"
    >
      <CModalHeader closeButton>
        <h5>Gerenciar Tipos de Termos</h5>
      </CModalHeader>

      <CModalBody>
        <CRow>
          {/* Lista de Tipos de Termos */}
          <CCol md="4">
            <CCard>
              <CCardBody>
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h6 className="mb-0">Tipos de Termos</h6>
                  <i
                    className="cil-plus"
                    style={{
                      cursor: "pointer",
                      fontSize: "18px",
                      color: "#007bff",
                    }}
                    title="Adicionar novo tipo de termo"
                    onClick={() => {
                      // Função para adicionar novo termo será implementada aqui
                      console.log("Adicionar novo tipo de termo");
                    }}
                  />
                </div>
                {loading ? (
                  <LoadingComponent />
                ) : (
                  <div style={{ maxHeight: "400px", overflowY: "auto" }}>
                    {tiposTermos.map((termo) => (
                      <div key={termo.id} className="mb-2">
                        <div
                          className={`p-2 border rounded ${
                            selectedTermo?.id === termo.id
                              ? "bg-primary text-white"
                              : "bg-light"
                          }`}
                          style={{
                            cursor:
                              termo.conteudo && termo.conteudo.length > 0
                                ? "pointer"
                                : "default",
                          }}
                          onClick={() => {
                            if (termo.conteudo && termo.conteudo.length > 0) {
                              toggleExpandTermo(termo.id);
                            }
                          }}
                        >
                          <div className="d-flex justify-content-between align-items-center">
                            <div className="d-flex align-items-center">
                              {termo.conteudo && termo.conteudo.length > 0 && (
                                <i
                                  className={`cil-chevron-${
                                    expandedTermos.has(termo.id)
                                      ? "bottom"
                                      : "right"
                                  } mr-2`}
                                />
                              )}
                              <strong>{termo.nome}</strong>
                            </div>
                            <div>
                              <i
                                className="cil-pencil"
                                style={{ cursor: "pointer" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectTermo(termo);
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {expandedTermos.has(termo.id) && (
                          <div className="ml-4 mt-2">
                            {termo.conteudo.map((c, i) => (
                              <div
                                key={i}
                                className={`p-2 mb-1 border rounded ${
                                  selectedTermo?.id === termo.id
                                    ? "bg-info text-white"
                                    : "bg-white"
                                }`}
                                style={{
                                  cursor: "pointer",
                                  marginLeft: "20px",
                                }}
                                onClick={() => handleSelectTermo(termo)}
                              >
                                <div className="d-flex justify-content-between align-items-center">
                                  <div>
                                    <strong>{c.nome}</strong>
                                  </div>
                                  <i
                                    className="cil-pencil"
                                    style={{ cursor: "pointer" }}
                                  />
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CCardBody>
            </CCard>
          </CCol>

          {/* Conteúdo de Edição */}
          <CCol md="8">
            <CCard>
              <CCardBody>
                {selectedTermo ? (
                  <div>
                    {loadingConteudo ? (
                      <LoadingComponent />
                    ) : (
                      <>
                        <h6 className="mb-3">Editar Tipo de Termo</h6>

                        <div className="row mb-3">
                          <div className="col-md-12">
                            <label htmlFor="nome">Nome:</label>
                            <CInput
                              id="nome"
                              value={editingTermo?.nome || ""}
                              disabled={true}
                              placeholder="Nome do tipo de termo"
                            />
                          </div>
                        </div>

                        <div className="mb-3">
                          <p>Tags disponiveis:</p>
                          <div className="row">
                            <div className="col">
                              <p>
                                <ul className="text-primary">
                                  <li>{"{ClientePrincipal}"}</li>
                                  <li>{"{TipoAcao}"}</li>
                                  <li>{"{AdversoPrincipal}"}</li>
                                  <li>{"{GrupoCotaContrato}"}</li>
                                  <li>{"{NrParcelasVencidas}"}</li>
                                  <li>{"{ValorParcelasVencidas}"}</li>
                                  <li>{"{MultaJuros}"}</li>
                                  <li>{"{Custas}"}</li>
                                  <li>{"{NrParcelasVincendas}"}</li>
                                  <li>{"{ValorParcelasVincendas}"}</li>
                                  <li>{"{Honorarios}"}</li>
                                </ul>
                              </p>
                            </div>
                            <div className="col">
                              <p>
                                <ul className="text-primary">
                                  <li>{"{Total}"}</li>
                                  <li>{"{DataBase}"}</li>
                                  <li>{"{QtdParcelasAcordadas}"}</li>
                                  <li>{"{ValorAcordado}"}</li>
                                  <li>{"{DescricaoVeiculo}"}</li>
                                  <li>{"{NrAtual}"}</li>
                                  <li>{"{JurisdicaoAtual}"}</li>
                                  <li>{"{HeaderBase64"}</li>
                                  <li>{"{FooterBase64}"}</li>
                                  <li>{"{Parcelas}"}</li>
                                </ul>
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="row mb-3">
                          <div className="col-md-12">
                            <label htmlFor="descricao">Descrição:</label>

                            <style>{`.tox-promotion { display: none !important; }`}</style>

                            <Editor
                              apiKey="0tikha1mpfegov516ubzkw3x7y9rm53pexmcj1if4s11cjcx" // opcional, mas recomendado
                              value={value}
                              onEditorChange={(newValue: string) =>
                                setValue(newValue)
                              }
                              init={{
                                height: 300,
                                menubar: true,
                                plugins: [
                                  "table",
                                  "lists",
                                  "link",
                                  "image",
                                  "code",
                                  "advlist",
                                  "autolink",
                                  "fullscreen",
                                ],
                                toolbar:
                                  "undo redo | blocks | bold italic underline | alignleft aligncenter alignright | bullist numlist | table | link image | code | fullscreen",
                                branding: false, // remove a marcação Tiny se quiser
                              }}
                            />
                          </div>
                        </div>

                        <div className="d-flex justify-content-between">
                          <CButton color="primary" onClick={handleSave}>
                            Salvar
                          </CButton>
                        </div>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-muted">
                    <h6>Selecione um tipo de termo para editar</h6>
                    <p>
                      Escolha um item da lista ao lado para visualizar e editar
                      suas informações.
                    </p>
                  </div>
                )}
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CModalBody>

      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ConteudoTermosModal;
