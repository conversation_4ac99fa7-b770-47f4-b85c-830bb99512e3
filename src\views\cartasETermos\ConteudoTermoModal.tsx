import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CCol,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CTextarea,
} from "@coreui/react";
import { getApi, putApi } from "src/reusable/functions";

import "react-quill/dist/quill.snow.css";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, PUT_DATA } from "src/api";

import { Editor } from "@tinymce/tinymce-react";
import LoadingComponent from "src/reusable/Loading";

interface TipoTermo {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
  conteudo: ConteudoTermoType[];
}

interface ConteudoTermoType {
  nome: string;
  crm: string;
  grupoId: Number;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ConteudoTermosModal = ({ isOpen, onClose }: Props) => {
  const [tiposTermos, setTiposTermos] = useState<TipoTermo[]>([]);

  const [selectedTermo, setSelectedTermo] = useState<TipoTermo | null>(null);
  const [selectedConteudoNome, setSelectedConteudoNome] = useState<
    string | null
  >(null);
  const [editingTermo, setEditingTermo] = useState<TipoTermo | null>(null);
  const [cabecalhoBase64, setCabecalhoBase64] = useState("");
  const [rodapeBase64, setRodapeBase64] = useState("");
  const [expandedTermos, setExpandedTermos] = useState<Set<string>>(new Set());

  const [loading, setLoading] = useState(false);
  const [loadingConteudo, setLoadingConteudo] = useState(false);
  const [conteudoTermoSelected, setConteudoTermoSelected] = useState(null);

  const [value, setValue] = useState("");

  const handleSelectTermo = (
    termo: TipoTermo,
    conteudo: ConteudoTermoType | null
  ) => {
    setSelectedConteudoNome(conteudo?.nome || null);
    setSelectedTermo(termo);
    setEditingTermo({ ...termo });
    getConteudoTermoByTipeTermo(termo.id).then((response) => {
      const content = response.find(
        (x: { grupoId: Number }) => x.grupoId === conteudo?.grupoId
      );
      setValue(content?.html);
      setConteudoTermoSelected(content);
      // Limpar uploads anteriores ao selecionar novo conteúdo
      setCabecalhoBase64("");
      setRodapeBase64("");
    });
  };

  const toggleExpandTermo = (termoId: string) => {
    setExpandedTermos((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(termoId)) {
        newSet.delete(termoId);
      } else {
        newSet.add(termoId);
      }
      return newSet;
    });
  };

  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove o prefixo "data:image/...;base64," para obter apenas o base64
        const base64 = result.split(",")[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  };

  const handleCabecalhoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const base64 = await convertToBase64(file);
        setCabecalhoBase64(base64);
      } catch (error) {
        console.error("Erro ao converter arquivo para base64:", error);
      }
    }
  };

  const handleRodapeUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const base64 = await convertToBase64(file);
        setRodapeBase64(base64);
      } catch (error) {
        console.error("Erro ao converter arquivo para base64:", error);
      }
    }
  };

  const handleSave = async () => {
    if (!editingTermo) return;

    setTiposTermos((prev) =>
      prev.map((termo) => (termo.id === editingTermo.id ? editingTermo : termo))
    );
    setSelectedTermo(editingTermo);
    await updateConteudoTermo();
  };

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo()]);
    await getTipoTermo();
    setLoading(false);
  }, []);

  const getTipoTermo = async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTiposTermos(response);
      } else {
        setTiposTermos([]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getConteudoTermoByTipeTermo = async (id: string) => {
    try {
      setLoadingConteudo(true);
      const response = await GET_DATA(
        getURI("getConteudoTermoByTipeTermo"),
        null,
        true,
        true,
        `/${id}`
      );
      setLoadingConteudo(false);
      return response;
    } catch (error) {
      console.error(error);
    }
    setLoadingConteudo(false);
  };

  const updateConteudoTermo = async () => {
    if (!conteudoTermoSelected) return;
    const formData = new FormData();
    formData.append("id", conteudoTermoSelected?.id);
    formData.append("html", value);
    formData.append(
      "cabecalhoImg",
      (cabecalhoBase64 || conteudoTermoSelected?.cabecalhoImg) ?? ""
    );
    formData.append(
      "rodapeImg",
      (rodapeBase64 || conteudoTermoSelected?.rodapeImg) ?? ""
    );
    formData.append("grupoId", conteudoTermoSelected?.grupoId || "");
    formData.append("crm", conteudoTermoSelected?.crm ?? "");
    //const res = await PUT_DATA(getURI("putConteudoTermo"), formData, true);

    const result = await fetch(getURI("putConteudoTermo"), {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${window.localStorage.getItem("token")}`,
      },
      body: formData,
    });
    const res = await result.json();

    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {};
  }, [asyncLoadFunc]);

  return (
    <CModal
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      size="xl"
      className="custom-modal modal-xxl"
    >
      <CModalHeader closeButton>
        <h5>Gerenciar Tipos de Termos</h5>
      </CModalHeader>

      <CModalBody>
        <CRow>
          {/* Lista de Tipos de Termos */}
          <CCol md="4">
            <CCard>
              <CCardBody>
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h6 className="mb-0">Tipos de Termos</h6>
                </div>
                {loading ? (
                  <LoadingComponent />
                ) : (
                  <div style={{ maxHeight: "500px", overflowY: "auto" }}>
                    {tiposTermos.map((termo) => (
                      <div key={termo.id} className="mb-2">
                        <div
                          className={`p-2 border rounded ${
                            selectedTermo?.id === termo.id
                              ? "bg-primary text-white"
                              : "bg-light"
                          }`}
                          style={{
                            cursor:
                              termo.conteudo && termo.conteudo.length > 0
                                ? "pointer"
                                : "default",
                          }}
                          onClick={() => {
                            if (termo.conteudo && termo.conteudo.length > 0) {
                              toggleExpandTermo(termo.id);
                            }
                          }}
                        >
                          <div className="d-flex justify-content-between align-items-center">
                            <div className="d-flex align-items-center">
                              {termo.conteudo && termo.conteudo.length > 0 && (
                                <i
                                  className={`cil-chevron-${
                                    expandedTermos.has(termo.id)
                                      ? "bottom"
                                      : "right"
                                  } mr-2`}
                                />
                              )}
                              <strong>{termo.nome}</strong>
                            </div>
                            <div className="d-flex align-items-center">
                              <i
                                className="cil-plus"
                                style={{
                                  cursor: "pointer",
                                  marginRight: "8px",
                                  color: "#28a745",
                                  fontWeight: "bold",
                                }}
                                title="Adicionar conteúdo ao termo"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  console.log(
                                    "Adicionar conteúdo ao termo:",
                                    termo.nome
                                  );
                                }}
                              />
                              <i
                                className="cil-pencil"
                                style={{ cursor: "pointer" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectTermo(termo, null);
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {expandedTermos.has(termo.id) && (
                          <div className="ml-4 mt-2">
                            {termo.conteudo.map((c, i) => (
                              <div
                                key={i}
                                className={`p-2 mb-1 border rounded ${
                                  selectedTermo?.id === termo.id &&
                                  selectedConteudoNome === c.nome
                                    ? "bg-info text-white"
                                    : "bg-white"
                                }`}
                                style={{
                                  cursor: "pointer",
                                  marginLeft: "20px",
                                }}
                                onClick={() => handleSelectTermo(termo, c)}
                              >
                                <div className="d-flex justify-content-between align-items-center">
                                  <div>
                                    <strong>{c.nome}</strong>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CCardBody>
            </CCard>
          </CCol>

          {/* Conteúdo de Edição */}
          <CCol md="8">
            <CCard>
              <CCardBody>
                {selectedTermo ? (
                  <div>
                    {loadingConteudo ? (
                      <LoadingComponent />
                    ) : (
                      <>
                        <h6 className="mb-3">Editar Tipo de Termo</h6>

                        <div className="row mb-3">
                          <div
                            className={`col-md-${
                              selectedConteudoNome !== null ? 4 : 12
                            }`}
                          >
                            <label htmlFor="nome">Nome:</label>
                            <CInput
                              id="nome"
                              value={editingTermo?.nome || ""}
                              disabled={true}
                              placeholder="Nome do tipo de termo"
                            />
                          </div>
                          {selectedConteudoNome !== null && (
                            <>
                              <div className="col-md-4">
                                <label htmlFor="nome">CRM:</label>
                                <CInput
                                  id="nome"
                                  value={conteudoTermoSelected?.crm || ""}
                                  disabled={true}
                                  placeholder="Nome do tipo de termo"
                                />
                              </div>
                              <div className="col-md-4">
                                <label htmlFor="nome">Grupo:</label>
                                <CInput
                                  id="nome"
                                  value={selectedConteudoNome || ""}
                                  disabled={true}
                                  placeholder="Nome do tipo de termo"
                                />
                              </div>
                            </>
                          )}
                        </div>

                        {/* Campos de Upload */}
                        <div className="row mb-3">
                          <div className="col-md-6">
                            <label htmlFor="cabecalho">
                              Cabeçalho (Imagem):
                            </label>
                            <input
                              type="file"
                              id="cabecalho"
                              className="form-control"
                              accept="image/*"
                              onChange={handleCabecalhoUpload}
                            />
                            {(cabecalhoBase64 ||
                              conteudoTermoSelected?.cabecalhoImg) && (
                              <div className="mt-2">
                                <small
                                  className={
                                    cabecalhoBase64
                                      ? "text-success"
                                      : "text-info"
                                  }
                                >
                                  {cabecalhoBase64
                                    ? "✓ Novo arquivo carregado"
                                    : "📎 Arquivo existente"}
                                </small>
                                <br />
                                <img
                                  src={`data:image/jpeg;base64,${
                                    cabecalhoBase64 ||
                                    conteudoTermoSelected?.cabecalhoImg
                                  }`}
                                  alt="Preview Cabeçalho"
                                  style={{
                                    maxWidth: "200px",
                                    maxHeight: "100px",
                                    marginTop: "5px",
                                    border: cabecalhoBase64
                                      ? "2px solid #28a745"
                                      : "1px solid #ccc",
                                  }}
                                />
                                {cabecalhoBase64 && (
                                  <div className="mt-1">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => setCabecalhoBase64("")}
                                    >
                                      Remover novo arquivo
                                    </button>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="col-md-6">
                            <label htmlFor="rodape">Rodapé (Imagem):</label>
                            <input
                              type="file"
                              id="rodape"
                              className="form-control"
                              accept="image/*"
                              onChange={handleRodapeUpload}
                            />
                            {(rodapeBase64 ||
                              conteudoTermoSelected?.rodapeImg) && (
                              <div className="mt-2">
                                <small
                                  className={
                                    rodapeBase64 ? "text-success" : "text-info"
                                  }
                                >
                                  {rodapeBase64
                                    ? "✓ Novo arquivo carregado"
                                    : "Arquivo existente"}
                                </small>
                                <br />
                                <img
                                  src={`data:image/jpeg;base64,${
                                    rodapeBase64 ||
                                    conteudoTermoSelected?.rodapeImg
                                  }`}
                                  alt="Preview Rodapé"
                                  style={{
                                    maxWidth: "200px",
                                    maxHeight: "100px",
                                    marginTop: "5px",
                                    border: rodapeBase64
                                      ? "2px solid #28a745"
                                      : "1px solid #ccc",
                                  }}
                                />
                                {rodapeBase64 && (
                                  <div className="mt-1">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => setRodapeBase64("")}
                                    >
                                      Remover novo arquivo
                                    </button>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="mb-3">
                          <p>Tags disponiveis:</p>
                          <div className="row">
                            <div className="col">
                              <p>
                                <ul className="text-primary">
                                  <li>{"{ClientePrincipal}"}</li>
                                  <li>{"{TipoAcao}"}</li>
                                  <li>{"{AdversoPrincipal}"}</li>
                                  <li>{"{GrupoCotaContrato}"}</li>
                                  <li>{"{NrParcelasVencidas}"}</li>
                                  <li>{"{ValorParcelasVencidas}"}</li>
                                  <li>{"{MultaJuros}"}</li>
                                  <li>{"{Custas}"}</li>
                                  <li>{"{NrParcelasVincendas}"}</li>
                                  <li>{"{ValorParcelasVincendas}"}</li>
                                </ul>
                              </p>
                            </div>
                            <div className="col">
                              <p>
                                <ul className="text-primary">
                                  <li>{"{Honorarios}"}</li>
                                  <li>{"{Total}"}</li>
                                  <li>{"{DataBase}"}</li>
                                  <li>{"{QtdParcelasAcordadas}"}</li>
                                  <li>{"{ValorAcordado}"}</li>
                                  <li>{"{DescricaoVeiculo}"}</li>
                                  <li>{"{NrAtual}"}</li>
                                  <li>{"{JurisdicaoAtual}"}</li>
                                  <li>{"{Parcelas}"}</li>
                                  <li>{"{Extras}"}</li>
                                </ul>
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="row mb-3">
                          <div className="col-md-12">
                            <label htmlFor="descricao">Descrição:</label>

                            <style>{`.tox-promotion { display: none !important; }`}</style>

                            <Editor
                              apiKey="0tikha1mpfegov516ubzkw3x7y9rm53pexmcj1if4s11cjcx"
                              value={value}
                              onEditorChange={(newValue: string) =>
                                setValue(newValue)
                              }
                              init={{
                                height: 300,
                                menubar: true,
                                plugins: [
                                  "table",
                                  "lists",
                                  "link",
                                  "image",
                                  "code",
                                  "advlist",
                                  "autolink",
                                  "fullscreen",
                                ],
                                toolbar:
                                  "undo redo | blocks | bold italic underline | alignleft aligncenter alignright | bullist numlist | table | link image | code | fullscreen",
                                branding: false,
                              }}
                            />
                          </div>
                        </div>

                        <div className="d-flex justify-content-between">
                          <CButton color="primary" onClick={handleSave}>
                            Salvar
                          </CButton>
                        </div>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-muted">
                    <h6>Selecione um tipo de termo para editar</h6>
                    <p>
                      Escolha um item da lista ao lado para visualizar e editar
                      suas informações.
                    </p>
                  </div>
                )}
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CModalBody>

      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ConteudoTermosModal;
