import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CTextarea,
} from "@coreui/react";
import { getApi, putApi, postApi, getApiInline } from "src/reusable/functions";
import Select from "react-select";

import "react-quill/dist/quill.snow.css";

import { Editor } from "@tinymce/tinymce-react";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import { getURI } from "src/config/apiConfig";

// Função auxiliar para buscar dados
const GetData = async (payload: any, endpoint: string = "") => {
  try {
    const response = await getApi(payload, endpoint);
    return response;
  } catch (error) {
    throw error;
  }
};

interface TipoTermo {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
  conteudo: ConteudoTermoType[];
}

interface ConteudoTermoType {
  nome: string;
  crm: string;
  grupoId: Number | null;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ConteudoTermosModal = ({ isOpen, onClose }: Props) => {
  const [tiposTermos, setTiposTermos] = useState<TipoTermo[]>([]);

  const [selectedTermo, setSelectedTermo] = useState<TipoTermo | null>(null);
  const [selectedConteudoNome, setSelectedConteudoNome] = useState<
    string | null
  >(null);
  const [editingTermo, setEditingTermo] = useState<TipoTermo | null>(null);
  const [cabecalhoFile, setCabecalhoFile] = useState<File | null>(null);
  const [rodapeFile, setRodapeFile] = useState<File | null>(null);
  const [cabecalhoPreview, setCabecalhoPreview] = useState("");
  const [rodapePreview, setRodapePreview] = useState("");
  const [expandedTermos, setExpandedTermos] = useState<Set<string>>(new Set());

  const [loading, setLoading] = useState(false);
  const [loadingConteudo, setLoadingConteudo] = useState(false);
  const [conteudoTermoSelected, setConteudoTermoSelected] = useState(null);

  const [value, setValue] = useState("");

  // Estados para inserção de novo layout
  const [showInsertModal, setShowInsertModal] = useState(false);
  const [selectedTermoForInsert, setSelectedTermoForInsert] =
    useState<TipoTermo | null>(null);
  const [newLayoutNome, setNewLayoutNome] = useState("");
  const [insertLoading, setInsertLoading] = useState(false);

  // Estados para CRM e Grupos
  const [crmsOptions, setCrmsOptions] = useState<any[]>([]);
  const [gruposOptions, setGruposOptions] = useState<any[]>([]);
  const [crmSelected, setCrmSelected] = useState<any>(null);
  const [grupoSelected, setGrupoSelected] = useState<any>(null);

  const handleSelectTermo = (
    termo: TipoTermo,
    conteudo: ConteudoTermoType | null
  ) => {
    setSelectedConteudoNome(conteudo?.nome || null);
    setSelectedTermo(termo);
    setEditingTermo({ ...termo });
    getConteudoTermoByTipeTermo(termo.id).then((response) => {
      const content = response.find(
        (x: { grupoId: Number | null; crm: string | null }) =>
          x.grupoId === (conteudo?.grupoId ?? null) &&
          x.crm === (conteudo?.crm ?? null)
      );
      console.log(conteudo);
      setValue(content?.html);
      setConteudoTermoSelected(content);
      setCabecalhoFile(null);
      setRodapeFile(null);
      setCabecalhoPreview("");
      setRodapePreview("");
    });
  };

  const toggleExpandTermo = (termoId: string) => {
    setExpandedTermos((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(termoId)) {
        newSet.delete(termoId);
      } else {
        newSet.add(termoId);
      }
      return newSet;
    });
  };

  const handleCabecalhoUpload = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setCabecalhoFile(file);
      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setCabecalhoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRodapeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setRodapeFile(file);
      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setRodapePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const openImageInNewTab = (base64Data: string, title: string) => {
    const dataUrl = `data:image/jpeg;base64,${base64Data}`;
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.title = title;
      newWindow.document.body.style.margin = "0";
      newWindow.document.body.style.display = "flex";
      newWindow.document.body.style.justifyContent = "center";
      newWindow.document.body.style.alignItems = "center";
      newWindow.document.body.style.minHeight = "100vh";
      newWindow.document.body.style.background = "#f5f5f5";

      const img = newWindow.document.createElement("img");
      img.src = dataUrl;
      img.style.maxWidth = "100%";
      img.style.maxHeight = "100%";
      img.style.objectFit = "contain";
      img.alt = title;

      newWindow.document.body.appendChild(img);
    }
  };

  // Funções para buscar CRMs e grupos
  const getCrms = useCallback(() => {
    return GetData({}, "getCrms")
      .then((res: any) => {
        if (res) return res;
      })
      .catch((err) => {
        console.warn(err);
        return null;
      });
  }, []);

  const getGrupoCrm = useCallback((crm: string) => {
    return GetData({ ActiveConnection: crm }, "getGrupoDataCobLista")
      .then((res: any) => {
        if (res && res !== null && res !== undefined) {
          return res;
        }
      })
      .catch((err) => {
        console.warn(err);
        return null;
      });
  }, []);

  const fetchGroupsForCrm = useCallback(
    (crm: any) => {
      return getGrupoCrm(crm.datacobName).then((groupData) => {
        crm.grupos = groupData;
        return crm;
      });
    },
    [getGrupoCrm]
  );

  const handleCrmsChange = (selectedCrm: any) => {
    setCrmSelected(selectedCrm);
    setGrupoSelected(null);
    if (selectedCrm && selectedCrm.grupos) {
      setGruposOptions(selectedCrm.grupos);
    } else {
      setGruposOptions([]);
    }
  };

  const handleGrupoChange = (selectedGrupo: any) => {
    setGrupoSelected(selectedGrupo);
  };

  const handleOpenInsertModal = (termo: TipoTermo) => {
    setSelectedTermoForInsert(termo);
    setNewLayoutNome("");
    setCrmSelected(null);
    setGrupoSelected(null);
    setGruposOptions([]);
    setShowInsertModal(true);

    // Carregar CRMs quando abrir o modal
    getCrms().then((crms) => {
      if (crms) {
        Promise.all(crms.map(fetchGroupsForCrm)).then((crmGroupData) => {
          setCrmsOptions(crmGroupData);
        });
      }
    });
  };

  const handleCloseInsertModal = () => {
    setShowInsertModal(false);
    setSelectedTermoForInsert(null);
    setNewLayoutNome("");
    setCrmSelected(null);
    setGrupoSelected(null);
    setGruposOptions([]);
  };

  const handleInsertLayout = async () => {
    if (!selectedTermoForInsert || !crmSelected || !grupoSelected) {
      toast.warning(
        "Por favor, preencha todos os campos obrigatórios (Nome, CRM e Grupo)."
      );
      return;
    }

    try {
      setInsertLoading(true);

      const payload = {
        tipoTermoId: selectedTermoForInsert.id,
        crm: crmSelected.datacobName || null,
        grupoId: grupoSelected.id_Grupo || null,
      };

      // Chamada para a API de inserção
      const response = await postApi(payload, "postConteudoTermoFromDefault");

      if (response?.success !== true) {
        throw new Error(response?.message || "Erro ao inserir layout");
      }

      // Recarregar os tipos de termo para mostrar o novo layout
      await getTipoTermo();

      handleCloseInsertModal();
      toast.success("Layout inserido com sucesso!");
    } catch (error) {
      console.error("Erro ao inserir layout:", error);
      toast.error("Erro ao inserir layout. Tente novamente.");
    } finally {
      setInsertLoading(false);
    }
  };

  const handleSave = async () => {
    if (!editingTermo) return;
    toast.info("Salvando conteúdo do termo...");

    setTiposTermos((prev) =>
      prev.map((termo) => (termo.id === editingTermo.id ? editingTermo : termo))
    );
    setSelectedTermo(editingTermo);
    if (await updateConteudoTermo())
      toast.success("Conteúdo do termo atualizado com sucesso!");
    else toast.error("Erro ao atualizar conteúdo do termo!");
  };

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo()]);
    await getTipoTermo();
    setLoading(false);
  }, []);

  const getTipoTermo = async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTiposTermos(response);
      } else {
        setTiposTermos([]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getConteudoTermoByTipeTermo = async (id: string) => {
    try {
      setLoadingConteudo(true);
      const response = await getApiInline(id, "getConteudoTermoByTipeTermo");
      setLoadingConteudo(false);
      return response;
    } catch (error) {
      console.error(error);
      setLoadingConteudo(false);
      return [];
    }
  };

  const updateConteudoTermo = async () => {
    if (!conteudoTermoSelected) return;
    const formData = new FormData();
    formData.append("id", conteudoTermoSelected?.id);
    formData.append("html", value);
    if (cabecalhoFile) {
      formData.append("cabecalhoImg", cabecalhoFile);
    } else if (conteudoTermoSelected?.cabecalhoImg) {
      formData.append("cabecalhoImg", conteudoTermoSelected.cabecalhoImg);
    }

    if (rodapeFile) {
      formData.append("rodapeImg", rodapeFile);
    } else if (conteudoTermoSelected?.rodapeImg) {
      formData.append("rodapeImg", conteudoTermoSelected.rodapeImg);
    }
    formData.append("grupoId", conteudoTermoSelected?.grupoId || "");
    formData.append("crm", conteudoTermoSelected?.crm ?? "");

    const result = await fetch(getURI("putConteudoTermo"), {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${window.localStorage.getItem("token")}`,
      },
      body: formData,
    });
    const res = await result.json();

    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {};
  }, [asyncLoadFunc]);

  return (
    <>
      <CModal
        show={isOpen}
        onClose={onClose}
        closeOnBackdrop={false}
        size="xl"
        className="custom-modal modal-xxl"
      >
        <CModalHeader closeButton>
          <h5>Gerenciar Tipos de Termos</h5>
        </CModalHeader>

        <CModalBody>
          <CRow>
            {/* Lista de Tipos de Termos */}
            <CCol md="4">
              <CCard>
                <CCardBody>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Tipos de Termos</h6>
                  </div>
                  {loading ? (
                    <LoadingComponent />
                  ) : (
                    <div style={{ maxHeight: "500px", overflowY: "auto" }}>
                      {tiposTermos.map((termo) => (
                        <div key={termo.id} className="mb-2">
                          <div
                            className={`p-2 border rounded ${
                              selectedTermo?.id === termo.id
                                ? "bg-primary text-white"
                                : "bg-light"
                            }`}
                            style={{
                              cursor:
                                termo.conteudo && termo.conteudo.length > 0
                                  ? "pointer"
                                  : "default",
                            }}
                            onClick={() => {
                              if (termo.conteudo && termo.conteudo.length > 0) {
                                toggleExpandTermo(termo.id);
                              }
                            }}
                          >
                            <div className="d-flex justify-content-between align-items-center">
                              <div className="d-flex align-items-center">
                                {termo.conteudo &&
                                  termo.conteudo.length > 0 && (
                                    <i
                                      className={`cil-chevron-${
                                        expandedTermos.has(termo.id)
                                          ? "bottom"
                                          : "right"
                                      } mr-2`}
                                    />
                                  )}
                                <strong>{termo.nome}</strong>
                              </div>
                              <div className="d-flex align-items-center">
                                <i
                                  className="cil-plus"
                                  style={{
                                    cursor: "pointer",
                                    marginRight: "8px",
                                    color: "#28a745",
                                    fontWeight: "bold",
                                  }}
                                  title="Adicionar conteúdo ao termo"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenInsertModal(termo);
                                  }}
                                />
                                <i
                                  className="cil-pencil"
                                  style={{ cursor: "pointer" }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSelectTermo(termo, null);
                                  }}
                                />
                              </div>
                            </div>
                          </div>

                          {expandedTermos.has(termo.id) && (
                            <div className="ml-4 mt-2">
                              {termo.conteudo.map((c, i) => (
                                <div
                                  key={i}
                                  className={`p-2 mb-1 border rounded ${
                                    selectedTermo?.id === termo.id &&
                                    selectedConteudoNome === c.nome
                                      ? "bg-info text-white"
                                      : "bg-white"
                                  }`}
                                  style={{
                                    cursor: "pointer",
                                    marginLeft: "20px",
                                  }}
                                  onClick={() => handleSelectTermo(termo, c)}
                                >
                                  <div className="d-flex justify-content-between align-items-center">
                                    <div>
                                      <strong>{c.nome}</strong>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CCardBody>
              </CCard>
            </CCol>

            {/* Conteúdo de Edição */}
            <CCol md="8">
              <CCard>
                <CCardBody>
                  {selectedTermo ? (
                    <div>
                      {loadingConteudo ? (
                        <LoadingComponent />
                      ) : (
                        <>
                          <h6 className="mb-3">Editar Tipo de Termo</h6>

                          <div className="row mb-3">
                            <div
                              className={`col-md-${
                                selectedConteudoNome !== null ? 4 : 12
                              }`}
                            >
                              <label htmlFor="nome">Nome:</label>
                              <CInput
                                id="nome"
                                value={editingTermo?.nome || ""}
                                disabled={true}
                                placeholder="Nome do tipo de termo"
                              />
                            </div>
                            {selectedConteudoNome !== null && (
                              <>
                                <div className="col-md-4">
                                  <label htmlFor="nome">CRM:</label>
                                  <CInput
                                    id="nome"
                                    value={conteudoTermoSelected?.crm || ""}
                                    disabled={true}
                                    placeholder="Nome do tipo de termo"
                                  />
                                </div>
                                <div className="col-md-4">
                                  <label htmlFor="nome">Grupo:</label>
                                  <CInput
                                    id="nome"
                                    value={selectedConteudoNome || ""}
                                    disabled={true}
                                    placeholder="Nome do tipo de termo"
                                  />
                                </div>
                              </>
                            )}
                          </div>

                          {/* Campos de Upload */}
                          <div className="row mb-3">
                            <div className="col-md-6">
                              <label htmlFor="cabecalho">
                                Cabeçalho (Imagem):
                              </label>
                              <input
                                type="file"
                                id="cabecalho"
                                className="form-control"
                                accept="image/*"
                                onChange={handleCabecalhoUpload}
                              />
                              {(cabecalhoFile ||
                                cabecalhoPreview ||
                                conteudoTermoSelected?.cabecalhoImg) && (
                                <div className="mt-2">
                                  <small
                                    className={
                                      cabecalhoFile
                                        ? "text-success"
                                        : "text-info"
                                    }
                                  >
                                    {cabecalhoFile
                                      ? "✓ Novo arquivo carregado"
                                      : "Arquivo existente"}
                                  </small>
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-primary me-2"
                                      onClick={() => {
                                        const imageData =
                                          cabecalhoPreview ||
                                          (conteudoTermoSelected?.cabecalhoImg
                                            ? `data:image/jpeg;base64,${conteudoTermoSelected.cabecalhoImg}`
                                            : null);
                                        if (imageData) {
                                          const newWindow = window.open();
                                          if (newWindow) {
                                            newWindow.document.title =
                                              "Cabeçalho - Visualização";
                                            newWindow.document.body.style.margin =
                                              "0";
                                            newWindow.document.body.style.display =
                                              "flex";
                                            newWindow.document.body.style.justifyContent =
                                              "center";
                                            newWindow.document.body.style.alignItems =
                                              "center";
                                            newWindow.document.body.style.minHeight =
                                              "100vh";
                                            newWindow.document.body.style.background =
                                              "#f5f5f5";

                                            const img =
                                              newWindow.document.createElement(
                                                "img"
                                              );
                                            img.src = imageData;
                                            img.style.maxWidth = "100%";
                                            img.style.maxHeight = "100%";
                                            img.style.objectFit = "contain";
                                            img.alt = "Cabeçalho";

                                            newWindow.document.body.appendChild(
                                              img
                                            );
                                          }
                                        }
                                      }}
                                    >
                                      <i className="cil-external-link me-1 font-weight-bold"></i>{" "}
                                      Visualizar Cabeçalho
                                    </button>
                                    {cabecalhoFile && (
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-outline-danger ml-1"
                                        onClick={() => {
                                          setCabecalhoFile(null);
                                          setCabecalhoPreview("");
                                        }}
                                      >
                                        Remover novo arquivo
                                      </button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="col-md-6">
                              <label htmlFor="rodape">Rodapé (Imagem):</label>
                              <input
                                type="file"
                                id="rodape"
                                className="form-control"
                                accept="image/*"
                                onChange={handleRodapeUpload}
                              />
                              {(rodapeFile ||
                                rodapePreview ||
                                conteudoTermoSelected?.rodapeImg) && (
                                <div className="mt-2">
                                  <small
                                    className={
                                      rodapeFile ? "text-success" : "text-info"
                                    }
                                  >
                                    {rodapeFile
                                      ? "✓ Novo arquivo carregado"
                                      : "Arquivo existente"}
                                  </small>
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-primary me-2"
                                      onClick={() => {
                                        const imageData =
                                          rodapePreview ||
                                          (conteudoTermoSelected?.rodapeImg
                                            ? `data:image/jpeg;base64,${conteudoTermoSelected.rodapeImg}`
                                            : null);
                                        if (imageData) {
                                          const newWindow = window.open();
                                          if (newWindow) {
                                            newWindow.document.title =
                                              "Rodapé - Visualização";
                                            newWindow.document.body.style.margin =
                                              "0";
                                            newWindow.document.body.style.display =
                                              "flex";
                                            newWindow.document.body.style.justifyContent =
                                              "center";
                                            newWindow.document.body.style.alignItems =
                                              "center";
                                            newWindow.document.body.style.minHeight =
                                              "100vh";
                                            newWindow.document.body.style.background =
                                              "#f5f5f5";

                                            const img =
                                              newWindow.document.createElement(
                                                "img"
                                              );
                                            img.src = imageData;
                                            img.style.maxWidth = "100%";
                                            img.style.maxHeight = "100%";
                                            img.style.objectFit = "contain";
                                            img.alt = "Rodapé";

                                            newWindow.document.body.appendChild(
                                              img
                                            );
                                          }
                                        }
                                      }}
                                    >
                                      <i className="cil-external-link me-1 font-weight-bold"></i>{" "}
                                      Visualizar Rodapé
                                    </button>
                                    {rodapeFile && (
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-outline-danger"
                                        onClick={() => {
                                          setRodapeFile(null);
                                          setRodapePreview("");
                                        }}
                                      >
                                        Remover novo arquivo
                                      </button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="mb-3">
                            <p>Tags disponiveis:</p>
                            <div className="row">
                              <div className="col">
                                <p>
                                  <ul className="text-primary">
                                    <li>{"{ClientePrincipal}"}</li>
                                    <li>{"{TipoAcao}"}</li>
                                    <li>{"{AdversoPrincipal}"}</li>
                                    <li>{"{GrupoCotaContrato}"}</li>
                                    <li>{"{NrParcelasVencidas}"}</li>
                                    <li>{"{ValorParcelasVencidas}"}</li>
                                    <li>{"{MultaJuros}"}</li>
                                    <li>{"{Custas}"}</li>
                                    <li>{"{NrParcelasVincendas}"}</li>
                                    <li>{"{ValorParcelasVincendas}"}</li>
                                  </ul>
                                </p>
                              </div>
                              <div className="col">
                                <p>
                                  <ul className="text-primary">
                                    <li>{"{Honorarios}"}</li>
                                    <li>{"{Total}"}</li>
                                    <li>{"{DataBase}"}</li>
                                    <li>{"{QtdParcelasAcordadas}"}</li>
                                    <li>{"{ValorAcordado}"}</li>
                                    <li>{"{DescricaoVeiculo}"}</li>
                                    <li>{"{NrAtual}"}</li>
                                    <li>{"{JurisdicaoAtual}"}</li>
                                    <li>{"{Parcelas}"}</li>
                                    <li>{"{Extras}"}</li>
                                  </ul>
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="row mb-3">
                            <div className="col-md-12">
                              <label htmlFor="descricao">Descrição:</label>

                              <style>{`.tox-promotion { display: none !important; }`}</style>

                              <Editor
                                apiKey="0tikha1mpfegov516ubzkw3x7y9rm53pexmcj1if4s11cjcx"
                                value={value}
                                onEditorChange={(newValue: string) =>
                                  setValue(newValue)
                                }
                                init={{
                                  height: 300,
                                  menubar: true,
                                  plugins: [
                                    "table",
                                    "lists",
                                    "link",
                                    "image",
                                    "code",
                                    "advlist",
                                    "autolink",
                                    "fullscreen",
                                  ],
                                  toolbar:
                                    "undo redo | blocks | bold italic underline | alignleft aligncenter alignright | bullist numlist | table | link image | code | fullscreen",
                                  branding: false,
                                }}
                              />
                            </div>
                          </div>

                          <div className="d-flex justify-content-between">
                            <CButton color="primary" onClick={handleSave}>
                              Salvar
                            </CButton>
                          </div>
                        </>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-muted">
                      <h6>Selecione um tipo de termo para editar</h6>
                      <p>
                        Escolha um item da lista ao lado para visualizar e
                        editar suas informações.
                      </p>
                    </div>
                  )}
                </CCardBody>
              </CCard>
            </CCol>
          </CRow>
        </CModalBody>

        <CModalFooter>
          <CButton color="secondary" onClick={onClose}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Modal de Inserção de Layout */}
      <CModal
        show={showInsertModal}
        onClose={handleCloseInsertModal}
        closeOnBackdrop={false}
        size="lg"
        className="custom-modal"
      >
        <CModalHeader closeButton>
          <h5>Adicionar Layout para {selectedTermoForInsert?.nome}</h5>
        </CModalHeader>

        <CModalBody>
          <div className="row mb-3">
            <div className="col-md-6">
              <label>CRM: *</label>
              <Select
                options={crmsOptions}
                value={crmSelected ?? null}
                onChange={handleCrmsChange}
                getOptionValue={(option) => option.id}
                getOptionLabel={(option) => option.datacobName}
                placeholder="Selecione o CRM"
                isClearable
              />
            </div>
            <div className="col-md-6">
              <label>Grupo: *</label>
              <Select
                options={gruposOptions}
                value={grupoSelected ?? null}
                onChange={handleGrupoChange}
                getOptionValue={(option) => option.id_Grupo}
                getOptionLabel={(option) => option.descricao}
                placeholder="Selecione o Grupo"
                isDisabled={!crmSelected}
                isClearable
              />
            </div>
          </div>

          <div className="alert alert-info">
            <strong>Informação:</strong> O layout será criado com conteúdo
            padrão. Após a criação, você poderá editá-lo clicando no ícone de
            lápis.
          </div>
        </CModalBody>

        <CModalFooter>
          <CButton
            color="success"
            onClick={handleInsertLayout}
            disabled={insertLoading || !crmSelected || !grupoSelected}
          >
            {insertLoading ? "Inserindo..." : "Inserir Layout"}
          </CButton>
          <CButton color="secondary" onClick={handleCloseInsertModal}>
            Cancelar
          </CButton>
        </CModalFooter>
      </CModal>
    </>
  );
};

export default ConteudoTermosModal;
