import React, { useState, useEffect, useCallback } from "react";
import {
  CLabel, CCol, CInput, CSwitch, CForm, CRow,
  CButton, CModal, CModalBody, CModalFooter, CModalHeader, CModalTitle, CFormGroup
} from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { GET_DATA } from "src/api";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import Select from "react-select";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { token } from "src/api";

const IntegrationConfigurationModal = ({ isOpen, editIntegrationConfiguration, onClose }) => {

  const [integrationconfiguration, setIntegrationConfiguration] = useState({
    id: null,
    groupId: null,
    crmId: null,
    active: true,
    started: format(new Date(), "yyyy-MM-dd", { locale: ptBR }),
    occurrence: false,
    agreement: false,
    bankSlip: false,
    linkedCrmId: null,
    linkedGroupId: null,
    allowConsult: false,
    allowInsert: false,
  });
  const [gruposOptions, setGruposOptions] = useState([]);
  const [gruposVinculadosOptions, setGruposVinculadosOptions] = useState([]);
  const [crmsVinculadosOptions, setCrmsVinculadosOptions] = useState([]);
  const [crmsOptions, setCrmsOptions] = useState([]);
  const [crmSelected, setCrmSelectd] = useState(null);
  const [crmVinculadoSelected, setCrmVinculadoSelected] = useState(null);
  const [grupoSelected, setGrupoSelected] = useState(null);
  const [grupoVinculadoSelected, setGrupoVinculadoSelected] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleInputNameChange = (e) => {
    const { name, value } = e.target;
    setIntegrationConfiguration((prevIntegrationConfiguration) => ({
      ...prevIntegrationConfiguration,
      [name]: value,
    }));
  };

  const handleCheckedChange = (e) => {
    const { name, checked } = e.target;
    setIntegrationConfiguration((prevIntegrationConfiguration) => ({
      ...prevIntegrationConfiguration,
      [name]: checked,
    }));
  };

  const handleCrmsChange = (selectedOptions) => {
    setGrupoSelected(null);
    setCrmSelectd(selectedOptions);
    setGruposOptions(selectedOptions.grupos);
    setIntegrationConfiguration((confg) => ({
      ...confg,
      crmId: selectedOptions.id,
    }));
    setCrmsVinculadosOptions((prev) =>
      prev.filter((a) => a.id !== selectedOptions.id)
    );
  };

  const handleGrupoChange = (selectedOptions) => {
    setGrupoSelected(selectedOptions);
    setIntegrationConfiguration((confg) => ({
      ...confg,
      groupId: selectedOptions.id_Grupo,
    }));
  };

  const handleCrmsVinculadosChange = (selectedOptions) => {
    setGrupoVinculadoSelected(null);
    setCrmVinculadoSelected(selectedOptions);
    setGruposVinculadosOptions(selectedOptions.grupos);
    setIntegrationConfiguration((confg) => ({
      ...confg,
      linkedCrmId: selectedOptions.id,
    }));
  };

  const handleGrupoVinculadosChange = (selectedOptions) => {
    setGrupoVinculadoSelected(selectedOptions);
    setIntegrationConfiguration((confg) => ({
      ...confg,
      linkedGroupId: selectedOptions.id_Grupo,
    }));
  };

  const isFieldEmpty = (value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  };

  const requiredFields = [
    { name: "groupId", displayName: "Grupo Crm" },
    { name: "crmId", displayName: "Crm" },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = integrationconfiguration[field.name];
      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    const newIntegrationConfiguration = {
      groupId: integrationconfiguration.groupId,
      crmId: integrationconfiguration.crmId,
      active: integrationconfiguration.active,
      started: integrationconfiguration.started
        ? integrationconfiguration.started
        : format(new Date(), "yyyy-MM-dd", { locale: ptBR }),
      occurrence: integrationconfiguration.occurrence,
      agreement: integrationconfiguration.agreement,
      bankSlip: integrationconfiguration.bankSlip,
      linkedCrmId: integrationconfiguration.linkedCrmId,
      linkedGroupId: integrationconfiguration.linkedGroupId,
      allowConsult: integrationconfiguration.allowConsult,
      allowInsert: integrationconfiguration.allowInsert,
    };

    handleCreateIntegrationConfiguration(newIntegrationConfiguration);
  };

  const handleCreateIntegrationConfiguration = async (
    newIntegrationConfiguration
  ) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/integrationconfiguration`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(newIntegrationConfiguration),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          toast.success("Configuração criada com sucesso");
          editIntegrationConfiguration = status.data;
          setIntegrationConfiguration(status.data);
          setIsLoading(false);
          handleClose();
        } else {
          toast.warning("Configuração já cadastrada");
          setIsLoading(false);
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro cadastrando da Configuração:", error);
      setIsLoading(false);
    }
  };

  const handleUpdateIntegrationConfiguration = async (
    integrationconfiguration
  ) => {
    try {
      setIsLoading(true);
      const url = `${getURI()}/integrationconfiguration`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify({
          ...integrationconfiguration,
          id: integrationconfiguration.id,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          setIsLoading(false);
          handleClose();
        }
      } else {
        console.error("Erro:", response?.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro editandar a Configuração:", error);
      setIsLoading(false);
    }
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleEdit = (e) => {
    e.preventDefault();
    const emptyFields = requiredFields.filter((field) => {
      const fieldValue = integrationconfiguration[field.name];

      return isFieldEmpty(fieldValue) || fieldValue === "0";
    });

    if (emptyFields.length > 0) {
      const fieldNames = emptyFields.map((field) => field.displayName);
      alert(`Por favor preencha os campos: ${fieldNames.join(", ")}`);
      return;
    }

    handleUpdateIntegrationConfiguration(integrationconfiguration);
  };

  const handleClose = () => {
    onClose();
  };

  function resetModal() {
    setIntegrationConfiguration({
      id: null,
      groupId: null,
      crmId: null,
      active: true,
      started: format(new Date(), "yyyy-MM-dd", { locale: ptBR }),
      occurrence: false,
      agreement: false,
      bankSlip: false,
      linkedCrmId: null,
      linkedGroupId: null,
      allowConsult: false,
      allowInsert: false,
    });
  }

  const getCrms = useCallback(() => {
    setIsLoading(true);
    return GetData({}, "getCrms")
      .then((res) => {
        if (res && res !== null && res !== undefined) {
          setIsLoading(false);
          return res;
        } else setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.warn(err);
        return null;
      });
  }, []);

  const getGrupoCrm = useCallback((crm) => {
    setIsLoading(true);
    return GetData({ ActiveConnection: crm }, "getGrupoDataCobLista")
      .then((res) => {
        if (res && res !== null && res !== undefined) {
          setIsLoading(false);
          return res;
        } else setIsLoading(false);
      })
      .catch((err) => {
        console.warn(err);
        setIsLoading(false);
        return null;
      });
  }, []);

  const fetchGroupsForCrm = useCallback(
    (crm) => {
      return getGrupoCrm(crm.datacobName).then((groupData) => {
        crm.grupos = groupData;
        return crm;
      });
    },
    [getGrupoCrm]
  );

  useEffect(() => {
    if (isOpen) {
      getCrms()
        .then((crms) => {
          if (crms) {
            return Promise.all(crms.map(fetchGroupsForCrm));
          }
          return [];
        })
        .then((crmGroupData) => {
          setCrmsOptions(crmGroupData);
          setCrmsVinculadosOptions(crmGroupData);

          if (editIntegrationConfiguration) {
            if (crmGroupData) {
              const crm = crmGroupData?.find(
                (a) => a.id === editIntegrationConfiguration.crmId
              );
              const crmName = crm?.datacobName || "N/A";
              setCrmSelectd({
                id: editIntegrationConfiguration.crmId,
                datacobName: crmName,
              });
              setGruposOptions(crm?.grupos || []);
              const groupName =
                crm?.grupos?.find(
                  (a) => a.id_Grupo === editIntegrationConfiguration.groupId
                )?.descricao || "N/A";
              setGrupoSelected({
                id_Grupo: editIntegrationConfiguration.groupId,
                descricao: groupName,
              });

              const linkedCrm = crmGroupData?.find(
                (a) => a.id === editIntegrationConfiguration.linkedCrmId
              );
              const linkedCrmName = linkedCrm?.datacobName || "N/A";
              setCrmVinculadoSelected({
                id: editIntegrationConfiguration.linkedCrmId,
                datacobName: linkedCrmName,
              });
              setGruposVinculadosOptions(linkedCrm?.grupos || []);
              const linkedGroupName =
                linkedCrm?.grupos?.find(
                  (a) =>
                    a.id_Grupo === editIntegrationConfiguration.linkedGroupId
                )?.descricao || "N/A";
              setGrupoVinculadoSelected({
                id_Grupo: editIntegrationConfiguration.linkedGroupId,
                descricao: linkedGroupName,
              });
            }

            setIntegrationConfiguration({
              id: editIntegrationConfiguration.id,
              groupId: editIntegrationConfiguration.groupId,
              crmId: editIntegrationConfiguration.crmId,
              linkedCrmId: editIntegrationConfiguration.linkedCrmId,
              linkedGroupId: editIntegrationConfiguration.linkedGroupId,
              active: editIntegrationConfiguration.active,
              started: format(
                new Date(editIntegrationConfiguration.started),
                "yyyy-MM-dd",
                { locale: ptBR }
              ),
              occurrence: editIntegrationConfiguration.occurrence,
              agreement: editIntegrationConfiguration.agreement,
              bankSlip: editIntegrationConfiguration.bankSlip,
              allowConsult: editIntegrationConfiguration.allowConsult,
              allowInsert: editIntegrationConfiguration.allowInsert,
            });
          } else resetModal();
        });
    }
  }, [isOpen, getCrms, fetchGroupsForCrm, editIntegrationConfiguration]);

  return (
    <>
      <CModal
        show={isOpen}
        onClose={handleClose}
        closeOnBackdrop={false}
        size="lg"
      >
        <CModalHeader closeButton>
          <CModalTitle>
            {integrationconfiguration?.id ?? false
              ? "Editar Configuração"
              : "Adicionar Configuração"}
          </CModalTitle>
        </CModalHeader>
        <CModalBody>
          {isLoading ? (
            <div>
              <LoadingComponent />
            </div>
          ) : (
            <CForm>
              <CFormGroup>
                <CLabel>CRM</CLabel>
                <Select
                  options={crmsOptions}
                  value={crmSelected ?? null}
                  onChange={handleCrmsChange}
                  getOptionValue={(option) => option.id}
                  getOptionLabel={(option) => option.datacobName}
                />
              </CFormGroup>
              <CFormGroup>
                <CLabel>Grupos CRM</CLabel>
                <Select
                  options={gruposOptions}
                  value={grupoSelected ?? null}
                  onChange={handleGrupoChange}
                  getOptionValue={(option) => option.id_Grupo}
                  getOptionLabel={(option) => option.descricao}
                />
              </CFormGroup>
              {grupoSelected && (
                <>
                  <CFormGroup>
                    <CLabel>CRM Vinculado</CLabel>
                    <Select
                      options={crmsVinculadosOptions}
                      value={crmVinculadoSelected ?? null}
                      onChange={handleCrmsVinculadosChange}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.datacobName}
                    />
                  </CFormGroup>
                  <CFormGroup>
                    <CLabel>Grupos CRM Vinculado</CLabel>
                    <Select
                      options={gruposVinculadosOptions}
                      value={grupoVinculadoSelected ?? null}
                      onChange={handleGrupoVinculadosChange}
                      getOptionValue={(option) => option.id_Grupo}
                      getOptionLabel={(option) => option.descricao}
                    />
                  </CFormGroup>
                </>
              )}
              <CFormGroup>
                <CLabel>Data início </CLabel>
                <CInput
                  id="started"
                  name="started"
                  type="date"
                  value={
                    integrationconfiguration.started ||
                    format(new Date(), "yyyy-MM-dd", { locale: ptBR })
                  }
                  placeholder="Email de Remetente"
                  onChange={handleInputNameChange}
                />
              </CFormGroup>
              <CRow>
                <CCol md="6">
                  <CFormGroup>
                    <CLabel
                      htmlFor="occurrence"
                      style={{ paddingRight: "20px" }}
                    >
                      Ocorrência
                    </CLabel>
                    <CSwitch
                      id="occurrence"
                      name="occurrence"
                      color="success"
                      checked={integrationconfiguration.occurrence || false}
                      onChange={handleCheckedChange}
                      shape="pill"
                      size="sm"
                      className={"pt-2"}
                    />
                    <CLabel style={{ paddingLeft: "20px" }}>
                      {" "}
                      {integrationconfiguration.occurrence
                        ? "Ativo"
                        : "Inativo"}{" "}
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup>
                    <CLabel
                      htmlFor="agreement"
                      style={{ paddingRight: "20px" }}
                    >
                      Acordo
                    </CLabel>
                    <CSwitch
                      id="agreement"
                      name="agreement"
                      color="success"
                      checked={integrationconfiguration.agreement || false}
                      onChange={handleCheckedChange}
                      shape="pill"
                      size="sm"
                      className={"pt-2"}
                    />
                    <CLabel style={{ paddingLeft: "20px" }}>
                      {" "}
                      {integrationconfiguration.agreement
                        ? "Ativo"
                        : "Inativo"}{" "}
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup>
                    <CLabel htmlFor="bankSlip" style={{ paddingRight: "20px" }}>
                      Boleto
                    </CLabel>
                    <CSwitch
                      id="bankSlip"
                      name="bankSlip"
                      color="success"
                      checked={integrationconfiguration.bankSlip || false}
                      onChange={handleCheckedChange}
                      shape="pill"
                      size="sm"
                      className={"pt-2"}
                    />
                    <CLabel style={{ paddingLeft: "20px" }}>
                      {" "}
                      {integrationconfiguration.bankSlip
                        ? "Ativo"
                        : "Inativo"}{" "}
                    </CLabel>
                  </CFormGroup>
                  <CFormGroup row>
                    <CCol>
                      <CLabel htmlFor="active" style={{ paddingRight: "20px" }}>
                        Status
                      </CLabel>
                      <CSwitch
                        id="active"
                        name="active"
                        color="success"
                        checked={integrationconfiguration.active || false}
                        onChange={handleCheckedChange}
                        shape="pill"
                        size="sm"
                        className={"pt-2"}
                      />
                      <CLabel style={{ paddingLeft: "20px" }}>
                        {" "}
                        {integrationconfiguration.active
                          ? "Ativo"
                          : "Inativo"}{" "}
                      </CLabel>
                    </CCol>
                  </CFormGroup>
                </CCol>
                <CCol md="6">
                  <CFormGroup row>
                    <CCol>
                      <CLabel htmlFor="active" style={{ paddingRight: "20px" }}>
                        Consulta
                      </CLabel>
                      <CSwitch
                        id="allowConsult"
                        name="allowConsult"
                        color="success"
                        checked={integrationconfiguration.allowConsult || false}
                        onChange={handleCheckedChange}
                        shape="pill"
                        size="sm"
                        className={"pt-2"}
                      />
                      <CLabel style={{ paddingLeft: "20px" }}>
                        {" "}
                        {integrationconfiguration.allowConsult
                          ? "Ativo"
                          : "Inativo"}{" "}
                      </CLabel>
                    </CCol>
                  </CFormGroup>
                  <CFormGroup row>
                    <CCol>
                      <CLabel htmlFor="active" style={{ paddingRight: "20px" }}>
                        Inserção
                      </CLabel>
                      <CSwitch
                        id="allowInsert"
                        name="allowInsert"
                        color="success"
                        checked={integrationconfiguration.allowInsert || false}
                        onChange={handleCheckedChange}
                        shape="pill"
                        size="sm"
                        className={"pt-2"}
                      />
                      <CLabel style={{ paddingLeft: "20px" }}>
                        {" "}
                        {integrationconfiguration.allowInsert
                          ? "Ativo"
                          : "Inativo"}{" "}
                      </CLabel>
                    </CCol>
                  </CFormGroup>
                </CCol>
              </CRow>
            </CForm>
          )}
        </CModalBody>
        <CModalFooter>
          {!integrationconfiguration?.id && (
            <CButton color="info" onClick={handleSubmit} disabled={isLoading}>
              <i className="cil-plus"></i> Adicionar Config
            </CButton>
          )}
          {integrationconfiguration?.id && (
            <>
              <CButton color="info" onClick={handleEdit} disabled={isLoading}>
                Salvar
              </CButton>
            </>
          )}
        </CModalFooter>
      </CModal>
    </>
  );
};

export default IntegrationConfigurationModal;
