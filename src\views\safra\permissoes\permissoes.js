import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ard, CCardBody, CButton, CRow, CCol } from "@coreui/react";
import CreateRoleModal from "./CreateRoleModal";
import { getURI } from "src/config/apiConfig";
import { useAuth } from "src/auth/AuthContext";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import TreeRole from "./partial/TreeRole";
import { PUT_DATA, token } from "src/api";
import { postApi } from "src/reusable/functions";

const SafraPermissoes = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Permissão de Campanhas",
  };

  const [permissionsData, setPermissionsData] = useState(null);
  const [editedPermissions, setEditedPermissions] = useState([]);
  const [isCreateModalOpen, setCreateModalOpen] = useState(false);

  const openModal = () => {
    setCreateModalOpen(true);
  };

  const closeModal = () => {
    setPermissionsData(null);
    setCreateModalOpen(false);
  };

  const getRoles = async () => {
    try {
      const response = await fetch(`${getURI()}/SafraCampaignPermissions`, {
        headers: {
          Authorization: `Bearer ${token()}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPermissionsData(data.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando funções:", error);
    }
  };

  const handlePermissionChange = (item) => {
    setEditedPermissions([
      ...editedPermissions.filter((x) => x.id !== item.id),
      item,
    ]);
  };

  const handleSavePermissions = async () => {
    for (const item of editedPermissions) {
      try {
        const response = await PUT_DATA("SafraCampaignPermissions", item);

        if (response?.success) {
          toast.success(`Usuário ${item.userName} atualizada com sucesso!`);
          setEditedPermissions([]);
          getRoles();
        } else {
          console.error("Erro:", response?.statusText);
          toast.error(
            `Usuário ${item.userName} não atualizado, tente novamente!`
          );
        }
      } catch (error) {
        console.error("Erro atualizando as permissões:", error);
        toast.error("Erro atualizando as permissões, tente novamente!");
      }
    }
  };

  const handleNewRole = async (newRole) => {
    try {
      const response = await postApi(newRole, "restSafraCampaignPermissions");

      if (response?.success) {
        toast.success("Permissão criada com sucesso!");
        getRoles();
      } else {
        console.error("Erro:", response?.statusText);
        toast.error("Falha ao criar nova permissão");
      }
    } catch (error) {
      console.error("Falha ao criar nova permissão:", error);
      toast.error("Falha ao criar nova permissão");
    }
  };

  const handleSaveRole = async (editedRole) => {
    try {
      const url = `${getURI()}/SafraCampaignPermissions`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(editedRole),
      });

      if (response.ok) {
        toast.success("Usuario atualizada com sucesso!");
        getRoles();
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro editando a usuario:", error);
    }
  };

  useEffect(() => {
    getRoles();
  }, []);

  return (
    <div>
      <h3>Permissões Safra Campanha</h3>
      <p style={{ color: "gray" }}>
        Defina as permissões dos usuários para as Campanhas Safra.
      </p>
      <CCard>
        <CCardBody>
          <CRow>
            <CCol md={12} className="text-right mb-2">
              <CButton
                color="info"
                onClick={openModal}
                title={inforPermissions(permissao).create}
                disabled={
                  !checkPermission(
                    permissao.modulo,
                    "Create",
                    permissao.submodulo
                  )
                }
              >
                <i
                  className={"cil-plus"}
                  size="sm"
                  style={{
                    border: "1px solid white",
                    borderRadius: "100%",
                    padding: "2px",
                  }}
                />{" "}
                Adicionar nova permissão
              </CButton>
              {isCreateModalOpen && (
                <CreateRoleModal
                  isOpen={isCreateModalOpen}
                  onRoleCreate={handleNewRole}
                  onClose={closeModal}
                  usersExist={permissionsData.map((x) => x.userId)}
                />
              )}
            </CCol>
          </CRow>
          {permissionsData && (
            <TreeRole
              initialItems={permissionsData}
              onCheckboxChange={handlePermissionChange}
            />
          )}
        </CCardBody>
        {editedPermissions.length > 0 && (
          <CCardFooter>
            <CButton
              color="info"
              onClick={handleSavePermissions}
              title={inforPermissions(permissao).edit}
              disabled={
                !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
              }
            >
              Salvar alterações
            </CButton>
          </CCardFooter>
        )}
      </CCard>
    </div>
  );
};

export default SafraPermissoes;
