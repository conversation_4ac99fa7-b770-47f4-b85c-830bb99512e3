import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>abel, CRow, CCol, CBadge, CButton, CTooltip } from "@coreui/react";
import { useHistory, Link } from "react-router-dom/cjs/react-router-dom";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, POST_DATA } from "src/api";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  calculateDaysDifference,
  formatCurrency,
  formatDate,
  formatThousands,
  formatDateGlobaltoSimplified,
} from "src/reusable/helpers";
import Select from "react-select";

import NegociacaoResumoModal from "src/views/abrirNegociacao/ResumoNegocicacoesModal";
import NegociacoesModal from "./AbrirNegociacaoModal/NegociacaoModal";
import { useMyContext } from "src/reusable/DataContext";
import { useAuth } from "src/auth/AuthContext";
import { calculaValoresParcelas } from "src/views/negociacao/utils/CalculosNegociacao";
import { liberarNegociacao } from "src/containers/AprovacaoJuridica";
import TipoCalcRNI from "./RNI/TipoCalcRNI";
import CalcTipoUmRNI from "./RNI/CalcTipoUmRNI";
import CalcTipoTresRNI from "./RNI/CalcTipoTresRNI";
import CalcTipoDoisRNI from "./RNI/CalcTipoDoisRNI";
import TermoJuridicoModal from "./TermoJuridico/TermoJuridicoModal";

const PostData = async (payload, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const ContratosAtivos = ({ selected }) => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissaoNegociacao = {
    modulo: "Negociação",
    submodulo: "Simular",
  };
  const permissaoNegociacaoSincronizar = {
    modulo: "Negociação",
    submodulo: "Buscar Parcelas",
  };

  const permissaoTermoJuridico = {
    modulo: "Cartas e Termos",
    submodulo: "Solicitar Termos Jurídicos",
  };

  const [temParcelasAbertas, setTemParcelasAbertas] = useState(false);
  const { data, updateContratos, contratos } = useMyContext();

  const [financiadoData, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  useEffect(() => {
    if (data) {
      setDadosFinanciados(data);
    }
  }, [data]);

  const [contratosData, setContratosData] = useState(
    localStorage.getItem("contratosAtivos")
      ? JSON.parse(localStorage.getItem("contratosAtivos"))
      : null
  );

  const [dadosNewcon] = useState(
    localStorage.getItem("newcon")
      ? JSON.parse(localStorage.getItem("newcon"))
      : []
  );

  const history = useHistory();

  const [isLoading, setIsLoading] = useState(false);
  const [expandedRows, setExpandedRows] = useState(
    getOpenContractToExpand(contratosData)
  );

  function getOpenContractToExpand(data) {
    return data !== null
      ? data.map((item) => {
          return item.parcelas.filter((fil) => {
            return fil.status === "A";
          }).length > 0
            ? item.id_Contrato
            : null;
        })
      : [];
  }
  const [explanationText, setExplanationText] = useState(false);
  const [showResumoModal, setShowResumoModal] = useState(false);
  const [showTermoJuridicoModal, setShowTermoJuridicoModal] = useState(false);

  const [showNegociacaoModal, setShowNegociacaoModal] = useState(false);
  const [dadosNegociacao, setDadosNegociacao] = useState(null);

  const [selectedStatus, setSelectedStatus] = useState("Ab");
  const [selectedTipo, setSelectedTipo] = useState("");
  const [tipoParcelaOptions, setTipoParcelaOptions] = useState([]);
  const [selectedAcordo, setSelectedAcordo] = useState("");

  const [totalOriginal, setTotalOriginal] = useState(0);
  const [totalAtualizado, setTotalAtualizado] = useState(0);

  const [showRniModal, setShowRniModal] = useState(false);
  const [showRniCalcTipoUmModal, setShowRniCalcTipoUmModal] = useState(false);
  const [showRniCalcTipoTresModal, setShowRniCalcTipoTresModal] =
    useState(false);
  const [showRniCalcTipoDoisModal, setShowRniCalcTipoDoisModal] =
    useState(false);
  const [tipoRni, setTipoRni] = useState(null);

  const [pressedRow, setPressedRow] = useState(null);
  const [mostrarBotaoCriaNegociacao, setMostrarBotaoCriaNegociacao] =
    useState(false);

  const [showButtonCalculadoraRni, setShowButtonCalculadoraRni] =
    useState(false);

  const [calculoValues, setCalculoValues] = useState([]);

  const handleStatusChange = (selectedOption) => {
    updateView(selectedOption.value == "" ? null : selectedOption.value).then(
      () => {
        setSelectedStatus(selectedOption.value);
      }
    );
  };

  useEffect(() => {
    HandleUpdateTotals(contratosData);
  }, [selectedTipo, selectedStatus, selectedAcordo]);

  const handleTipoChange = (selectedOption) => {
    setSelectedTipo(selectedOption.value);
  };

  const handleAcordorChange = (selectedOption) => {
    setSelectedAcordo(selectedOption.value);
  };

  const statusOptions = [
    { value: "", label: "Todas" },
    { value: "P", label: "Pago" },
    { value: "D", label: "Devolvido" },
    { value: "Ab", label: "Aberto" },
    { value: "Ac", label: "Acordo" },
  ];

  const acordoOptions = [
    { value: "", label: "Acordo" },
    { value: 1, label: "Com acordo" },
    { value: null, label: "Sem acordo" },
  ];

  const updateView = (statusInstallment = "Ab") => {
    return new Promise((resolve) => {
      const payload = {
        IdFinanciado: financiadoData.id_Financiado,
        IdAgrupamento: financiadoData.id_Agrupamento,
        statusInstallment: statusInstallment ?? "",
      };
      setIsLoading(true);

      getContratosAtivos(payload, "getContratosAtivos")
        .then(async (data) => {
          if (data) {
            localStorage.setItem("contratosAtivos", JSON.stringify(data));
            const calcValues = await getCalculoDatacob(data);
            updateContratos(data);
            setContratosData(data);
            setExpandedRows(getOpenContractToExpand(data));
            HandleUpdateTotals(data, calcValues);
          } else {
            setContratosData(null);
          }
        })
        .catch((err) => {
          console.log(err);
          resolve(false);
        })
        .finally(() => {
          setIsLoading(false);
          resolve(true);
        });
    });
  };

  async function getGruposCalculadoraRniConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "grupos_calculadora_rni"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return {
        rodobens: [],
        gvc: [],
      };
    }
  }

  async function canShowButtonCalculadoraRni() {
    const config = await getGruposCalculadoraRniConfig();
    let verif = false;

    try {
      if (financiadoData?.coddatacob === "GVC") {
        if (config.gvc.indexOf(financiadoData?.id_Grupo) > -1) verif = true;
      }
      if (financiadoData?.coddatacob === "Rodobens") {
        if (config.rodobens.indexOf(financiadoData?.id_Grupo) > -1)
          verif = true;
      }
    } catch (err) {
      console.warn("erro Config grupos_calculadora_rni", err);
      verif = false;
    }
    setShowButtonCalculadoraRni(verif);
  }

  const getContratosAtivos = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(async () => {
    if (financiadoData) {
      if (selected) {
        await updateView();
      }
    }
  }, [selected, financiadoData]);

  const toggleRow = (rowId) => {
    if (expandedRows.includes(rowId)) {
      setExpandedRows(expandedRows.filter((id) => id !== rowId));
      setExplanationText(false);
    } else {
      setExplanationText(true);
      setExpandedRows([...expandedRows, rowId]);
    }
  };

  function classificacaoName(contrato, rni) {
    if (!rni) return "";
    const id_classificacao = contrato.numero_Contrato[7];
    switch (id_classificacao) {
      case "1":
        return "Contrato de Venda";
      case "2":
        return "Conf. Divida";
      case "3":
        return "Conf. Custa";
      case "7":
        return "Conf. DB Encargo";
      case "8":
        return "Conf. Taxas";
      default:
        return "";
    }
  }

  const [loadingSincronizarParcelas, setLoadingSincronizarParcelas] =
    useState(false);
  const handleSincronizarParcelas = async (item) => {
    setLoadingSincronizarParcelas(true);
    const payload = {
      crm: financiadoData.coddatacob,
      documento: financiadoData.cpfCnpj,
      nrContrato: item.numero_Contrato,
      idContrato: item.id_Contrato,
    };
    try {
      const response = await POST_DATA(
        getURI("postSyncDatacobNewcon"),
        payload,
        true
      );
      if (response?.success) {
        await updateView();
      } else {
        toast.warning(response?.message ?? "Erro ao buscar parcelas");
      }
    } catch (error) {
      console.warn("error", error);
      toast.warning("Erro ao buscar parcelas");
    }
    setLoadingSincronizarParcelas(false);
  };

  const renderRow = (item) => {
    const isExpanded = expandedRows.includes(item.id_Contrato);
    const filteredData = filterInstallments(item.parcelas);
    let existeNumeroLongo = contratosData.some(
      (contrato) =>
        contrato.numero_Contrato.includes(item.numero_Contrato) &&
        contrato.numero_Contrato.length === 25 &&
        item.numero_Contrato !== contrato.numero_Contrato &&
        contrato?.grupo?.toLowerCase()?.includes("safra") &&
        contrato.parcelas.filter((x) => x.status === "A").length > 0
    );
    if (filteredData.length > 0 && !existeNumeroLongo) {
      return (
        <React.Fragment key={`Fragment${item.id_Contrato}`}>
          <tr onClick={() => toggleRow(item.id_Contrato)}>
            <td colSpan={15} style={{ cursor: "pointer" }}>
              {" "}
              {expandedRows.includes(item.id_Contrato) ? (
                <i className="cil-minus mr-2 gray-tint-icon" />
              ) : (
                <i className="cil-plus mr-2 gray-tint-icon" />
              )}
              Nº Contrato: <strong>{item.numero_Contrato} </strong>{" "}
              {" - Nº Parcelas: "}
              <strong>{filteredData.length} </strong> {" - Cliente: "}
              <strong>{item.cliente} </strong>{" "}
              <CBadge color="primary" key={`CBadge${item.id_Contrato}`}>
                {classificacaoName(item, showButtonCalculadoraRni)}
              </CBadge>
              {dadosNewcon?.nmSituacaoCobranca ? renderStatusNewcon() : ""}
              {checkPermission(
                permissaoNegociacaoSincronizar.modulo,
                "Create",
                permissaoNegociacaoSincronizar.submodulo
              ) && (
                <>
                  {" - "}
                  <CButton
                    className={"py-0"}
                    color="primary"
                    onClick={() => handleSincronizarParcelas(item)}
                    disabled={loadingSincronizarParcelas}
                  >
                    {!loadingSincronizarParcelas
                      ? "Buscar parcelas"
                      : "Sincronizando..."}
                  </CButton>
                </>
              )}
              {item !== undefined && item.campanha !== undefined
                ? renderIndicadoresCampanhas(item)
                : ""}
            </td>
          </tr>
          {isExpanded && renderInnerRows(item.parcelas)}
        </React.Fragment>
      );
    } else {
      return <></>;
    }
  };

  const renderStatusNewcon = () => {
    return (
      <>
        - Status:{" "}
        <CBadge
          color="secondary"
          key={`Status-${dadosNewcon?.nmSituacaoCobranca}`}
        >
          {" "}
          {dadosNewcon?.nmSituacaoCobranca}
        </CBadge>
      </>
    );
  };

  const renderIndicadoresCampanhas = (item) => {
    if (item.campanha !== null && item.campanha.length > 0) {
      return (
        <>
          - Campanhas:{" "}
          {item.campanha?.map((val, index) => (
            <>
              {" "}
              <CBadge
                color="danger"
                key={`CBadgeCamp${item.id_Contrato}_${index}`}
              >
                {" "}
                {val?.mensagem}
              </CBadge>
            </>
          ))}
        </>
      );
    }
  };

  const temAcordo = (item) => {
    if (item.status === "A") {
      if (item.nr_Acordo > 0) {
        return true;
      } else {
        return false;
      }
    }
  };

  const handleRowClick = (parcelaId) => {
    setPressedRow(parcelaId);
  };

  const handleRowDoubleClick = (negociacaoData) => {
    if (
      !checkPermission(
        permissaoNegociacao.modulo,
        "View",
        permissaoNegociacao.submodulo
      )
    ) {
      toast.warning(inforPermissions(permissaoNegociacao).view, {
        autoClose: 8000,
      });
      return;
    }
    setDadosNegociacao(negociacaoData);
    setShowNegociacaoModal(true);
  };

  function compararParcelas(a, b) {
    // Primeiro, compare pelo status ascendente
    const statusA = a.status;
    const statusB = b.status;

    if (statusA < statusB) {
      return -1;
    }
    if (statusA > statusB) {
      return 1;
    }

    // Se os status forem iguais, compare pelo nr_Acordo descendente
    const nrAcordoA = b.nr_Acordo - a.nr_Acordo;

    if (nrAcordoA !== 0) {
      return nrAcordoA;
    }

    // Se os números de acordo forem iguais, compare pelo atraso descendente
    return b.atraso - a.atraso;
  }

  const filterInstallments = (parcelas) => {
    const sortData = (parcela) => {
      const sortedData = [...parcela];
      //sortedData.sort((a, b) => a.nr_Parcela - b.nr_Parcela);
      sortedData.sort(compararParcelas);

      return sortedData;
    };
    const parcelaOrganizada = sortData(parcelas);

    const filteredData = parcelaOrganizada.filter((item) => {
      const matchesStatus =
        !selectedStatus ||
        item.status === selectedStatus ||
        (selectedStatus === "Ac" && temAcordo(item)) ||
        (selectedStatus === "Ab" && item.status === "A" && !temAcordo(item));

      const matchesTipo =
        !selectedTipo ||
        item.nome_Tipo_Parcela === selectedTipo ||
        (selectedTipo === "" && item.nome_Tipo_Parcela);

      const matchesAcordo =
        // !selectedAcordo ||
        (item.nr_Acordo > 0 && selectedAcordo) ||
        (!item.nr_Acordo && !selectedAcordo) ||
        selectedAcordo === "";
      return matchesStatus && matchesTipo && matchesAcordo;
    });
    return filteredData;
  };

  const updateParcelaValue = (parcela, valueManual = null) => {
    const calculo = valueManual ?? calculoValues;
    if (calculo.length > 0) {
      const calculoParcela = calculo
        .find((x) => x.nrContrato === parcela.numero_Contrato)
        ?.parcelas?.find((item) => item.idParcela === parcela.id_Parcela);
      if (calculoParcela) {
        parcela.vl_Atualizado = calculoParcela.vlAtualizado;
        parcela.vl_Desc_Max = calculoParcela.vlAtualizadoDescontoMax;
        parcela.atraso = calculoParcela.atraso;
        parcela.vl_Saldo = calculoParcela.vlSaldo;
        parcela.vl_Original = calculoParcela.vlOriginal;
      }
    }
    return parcela;
  };

  const chooseDtParcela = (parcela) => {
    if (parcela.dt_Venc_Boleto === null && parcela.dt_Pix !== null) {
      return formatDate(parcela.dt_Pix);
    }
    if (parcela.dt_Venc_Boleto !== null && parcela.dt_Pix === null) {
      return formatDate(parcela.dt_Venc_Boleto);
    }
    if (parcela.dt_Venc_Boleto !== null && parcela.dt_Pix !== null) {
      const dtBoleto = new Date(parcela.dt_Venc_Boleto);
      const dtPix = new Date(parcela.dt_Pix);
      if (dtBoleto > dtPix) return formatDate(parcela.dt_Venc_Boleto);
      return formatDate(parcela.dt_Pix);
    } else {
      return "---";
    }
  };

  const renderInnerRows = (parcelas) => {
    const filteredData = filterInstallments(parcelas);

    return filteredData.map((parcela) => {
      parcela = updateParcelaValue(parcela);
      return (
        <tr
          className={`expanded-table ${
            pressedRow === parcela.id_Parcela ? "pressed" : ""
          }`}
          key={`tr${parcela.id_Parcela}_${parcela.id_Contrato}`}
          onClick={() => handleRowClick(parcela.id_Parcela)}
          onDoubleClick={() => handleRowDoubleClick(parcela)}
        >
          <td style={{ borderLeft: "0" }}>
            {parcela.status === "P" ? (
              <CBadge
                color={"success"}
                key={`CBadgeParc${parcela.id_Parcela}_${parcela.id_Contrato}`}
              >
                Pago
              </CBadge>
            ) : parcela.status === "D" ? (
              <CBadge
                style={{ backgroundColor: "#9000ff", color: "white" }}
                key={`CBadgeDev${parcela.id_Parcela}_${parcela.id_Contrato}`}
              >
                Devolvido
              </CBadge>
            ) : parcela.nr_Acordo ? (
              <CBadge
                color={"info"}
                key={`CBadgeAcor${parcela.id_Parcela}_${parcela.id_Contrato}`}
              >
                Acordo
              </CBadge>
            ) : (
              <CBadge
                color={parcela.atraso > 0 ? "danger" : "warning"}
                key={`CBadgeAtra${parcela.id_Parcela}_${parcela.id_Contrato}`}
              >
                Aberto
              </CBadge>
            )}
          </td>
          <td>{parcela.nr_Parcela}</td>
          <td>{parcela.nr_Plano}</td>
          <td>{parcela.nome_Tipo_Parcela}</td>
          <td>
            {parcela.dt_Vencimento ? formatDate(parcela.dt_Vencimento) : "---"}
          </td>
          <td>
            {parcela.vl_Saldo ? formatThousands(parcela.vl_Saldo) : "0,00"}
          </td>
          <td>
            {parcela.vl_Original
              ? formatThousands(parcela.vl_Original)
              : "0,00"}
          </td>
          <td>
            {parcela.vl_Atualizado
              ? formatCurrency(parcela.vl_Atualizado, false)
              : "0,00"}
          </td>
          <td>
            {parcela.vl_Atualizado
              ? formatCurrency(parcela.vl_Desc_Max, false)
              : "0,00"}
          </td>
          <td>{parcela.atraso}</td>
          <td>
            {parcela.dt_Inclusao
              ? calculateDaysDifference(
                  new Date(),
                  new Date(parcela.dt_Inclusao)
                )
              : "---"}
          </td>
          <td>
            {parcela.nr_Acordo ? (
              <CTooltip content="Visualizar acordo" placement="top-end">
                <CButton
                  color="info"
                  className="py-0"
                  key={`CTooltipCButton${parcela.id_Acordo}`}
                  onClick={() =>
                    history.push(`/acordos/visualizar/${parcela.id_Acordo}`)
                  }
                >
                  {parcela.nr_Acordo}
                </CButton>
              </CTooltip>
            ) : (
              "---"
            )}
          </td>
          <td>
            {parcela.dt_Negociacao ? formatDate(parcela.dt_Negociacao) : "---"}
          </td>
          <td>{chooseDtParcela(parcela)}</td>
          <td>{parcela.qtde_Boleto_Emitido}</td>
        </tr>
      );
    });
  };

  useEffect(() => {
    if (contratosData) {
      HandleUpdateTotals(contratosData);
      const uniqueTipoParcelas = [
        ...new Set(
          contratosData.flatMap((item) =>
            item.parcelas.map((parcela) => parcela.nome_Tipo_Parcela)
          )
        ),
      ];
      const optionsTipoParcela = [
        { value: "", label: "Tipo" },
        ...uniqueTipoParcelas.map((Tipo) => ({
          value: Tipo,
          label: Tipo,
        })),
      ];
      setTipoParcelaOptions(optionsTipoParcela);
    }
    canShowButtonCalculadoraRni();
  }, []);

  useEffect(() => {
    if (contratos) {
      const temParcelaAberta = contratos.some((contrato) =>
        contrato.parcelas.some((parcela) => parcela.status === "A")
      );
      setTemParcelasAbertas(temParcelaAberta);
    }
  }, [data, contratos]);

  const HandleUpdateTotals = (dadosAtualizar, calcValues = null) => {
    if (dadosAtualizar) {
      const somaSaldos = dadosAtualizar.map((item) => {
        item.parcelas = item.parcelas.map((parcela) => {
          parcela = updateParcelaValue(parcela, calcValues);
          return parcela;
        });
        const sumOriginal = item.parcelas.reduce((accumulator, subItem) => {
          if (subItem.status === "A" && selectedAcordo != null) {
            return accumulator + subItem.vl_Original;
          }
          return accumulator;
        }, 0);

        const somaAtualizados = item.parcelas.reduce((accumulator, subItem) => {
          if (subItem.status === "A") {
            return accumulator + Math.round(subItem.vl_Atualizado * 100) / 100;
          }
          return accumulator;
        }, 0);

        return { id: item.id_Contrato, sumOriginal, somaAtualizados };
      });

      const sumOriginal = somaSaldos.reduce((accumulator, currentItem) => {
        return accumulator + currentItem.sumOriginal;
      }, 0);

      const somaAtualizados = somaSaldos.reduce((accumulator, currentItem) => {
        return accumulator + currentItem.somaAtualizados;
      }, 0);

      setTotalOriginal(sumOriginal);

      setTotalAtualizado(somaAtualizados);
    }
  };

  const getCalculoDatacob = async () => {
    const payload = {
      idAgrupamento: financiadoData.id_Agrupamento,
      dtNegociacao: formatDateGlobaltoSimplified(new Date()),
      vlNegociado: 0,
      parcelas: [],
      formaDesconto: 0,
      crm: financiadoData.coddatacob,
    };
    try {
      const response = await PostData(payload, "postCalcularNegociacao");
      console.log("response", payload, financiadoData);
      if (
        response.data &&
        response.data.negociacaoDto &&
        response.data.negociacaoDto.length > 0
      ) {
        console.log("response.data.negociacaoDto", response.data.negociacaoDto);
        setCalculoValues(response.data.negociacaoDto);
        return response.data.negociacaoDto;
      }
      if (response.success === false) {
        setCalculoValues([]);
      }
    } catch (error) {
      console.warn("error", error);
      setCalculoValues([]);
    }
    return [];
  };

  useEffect(() => {
    let isMounted = true; // Flag para verificar se o componente está montado

    async function fetchData() {
      const liberar = await liberarNegociacao(data);
      if (isMounted) {
        // Só atualiza o estado se ainda estiver montado
        if (liberar === true) {
          setMostrarBotaoCriaNegociacao(true);
        } else {
          setMostrarBotaoCriaNegociacao(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false; // Marca como desmontado na limpeza
    };
  }, [data]);

  const handleCloseTipoRniModal = (tipo = null) => {
    setShowRniModal(false);
    if (tipo === 1) setShowRniCalcTipoUmModal(true);
    if (tipo === 3) setShowRniCalcTipoTresModal(true);
    if (tipo === 2) setShowRniCalcTipoDoisModal(true);
  };

  return (
    <>
      {isLoading ? (
        <CardLoading />
      ) : (
        <>
          {contratosData ? (
            <>
              <CRow>
                <CCol
                  className="mt-1"
                  sm={financiadoData?.grupo === "Banco Safra" ? "9" : "6"}
                  xl={financiadoData?.grupo === "Banco Safra" ? "7" : ""}
                >
                  <CButton
                    color="info"
                    title={inforPermissions(permissaoNegociacao).view}
                    disabled={
                      !checkPermission(
                        permissaoNegociacao.modulo,
                        "View",
                        permissaoNegociacao.submodulo
                      )
                    }
                    onClick={() => setShowResumoModal(true)}
                  >
                    Ver Negociação
                  </CButton>
                  {mostrarBotaoCriaNegociacao && (
                    <>
                      <Link to="/negociar/cotas">
                        <CButton
                          color="success"
                          className="ml-2"
                          disabled={!temParcelasAbertas}
                        >
                          Criar Negociação
                        </CButton>
                      </Link>
                      {financiadoData?.grupo === "Banco Safra" && (
                        <Link to="/negociar/safra">
                          <CButton
                            color="info"
                            className="ml-2"
                            disabled={!temParcelasAbertas}
                          >
                            Criar Negociação Safra
                          </CButton>
                        </Link>
                      )}
                      {checkPermission("Negociação", "Create", "BBC") && (
                        <Link to="/negociar/BBC">
                          <CButton
                            color="info"
                            className="ml-2"
                            disabled={!temParcelasAbertas}
                          >
                            Criar Negociação BBC
                          </CButton>
                        </Link>
                      )}
                      {checkPermission(
                        permissaoTermoJuridico.modulo,
                        "View",
                        permissaoTermoJuridico.submodulo
                      ) && (
                        <CButton
                          color="danger"
                          className="ml-2"
                          onClick={() => setShowTermoJuridicoModal(true)}
                        >
                          Solicitar Termos Jurídicos
                        </CButton>
                      )}
                    </>
                  )}
                  {showButtonCalculadoraRni && (
                    <CButton
                      color="info"
                      className="ml-2"
                      style={{
                        backgroundColor: "rgb(36 179 242)",
                        borderColor: "rgb(36 179 242)",
                      }}
                      onClick={() => setShowRniModal(true)}
                    >
                      Calculadora RNI
                    </CButton>
                  )}
                  {contratosData.length > 1 && (
                    <>
                      <strong className="text-danger m-2">
                        Atenção! Este cliente possui mais de um contrato.
                      </strong>
                    </>
                  )}
                </CCol>
                <CCol className="information-text">
                  {explanationText
                    ? "Dê um duplo clique com o botão esquerdo do mouse para ver os detalhes das informações."
                    : ""}
                </CCol>
              </CRow>
              <CRow className="my-1">
                <CCol col="4" className="d-flex">
                  <div className="flex-grow-1">
                    <Select
                      className="mr-1"
                      value={statusOptions.find(
                        (option) => option.value === selectedStatus
                      )}
                      options={statusOptions}
                      onChange={handleStatusChange}
                      placeholder="Status"
                    />
                  </div>
                  <div className="flex-grow-1">
                    <Select
                      className="mr-1"
                      value={tipoParcelaOptions.find(
                        (option) => option.value === selectedTipo
                      )}
                      options={tipoParcelaOptions}
                      onChange={handleTipoChange}
                      placeholder="Tipo"
                    />
                  </div>
                  <div className="flex-grow-1">
                    <Select
                      className="mr-1"
                      value={acordoOptions.find(
                        (option) => option.value === selectedAcordo
                      )}
                      options={acordoOptions}
                      onChange={handleAcordorChange}
                      placeholder="Com Acordo"
                    />
                  </div>
                </CCol>
                <CCol col="8" className="mt-auto">
                  <div className="d-flex justify-content-end">
                    <CLabel className="mr-2">
                      Valor total original:{" "}
                      <strong>{formatCurrency(totalOriginal)}</strong>
                    </CLabel>
                    <CLabel>
                      Valor total atualizado:{" "}
                      <strong>{formatCurrency(totalAtualizado)}</strong>
                    </CLabel>
                  </div>
                </CCol>
              </CRow>
              <div className="table-container" style={{ minHeight: "200px" }}>
                <table className="table tabs-table">
                  <thead>
                    <tr>
                      <th>Status</th>
                      <th>Parcela</th>
                      <th>Plano</th>
                      <th>Tipo</th>
                      <th>Vencimento</th>
                      <th>Saldo</th>
                      <th>Original</th>
                      <th>Atualizado</th>
                      <th>Desc. Máximo</th>
                      <th>Atraso</th>
                      <th>Entrada</th>
                      <th>Nr. Acordo</th>
                      <th>Dt. Negociação</th>
                      <th>Dt. Boleto/Pix</th>
                      <th>Qt. Boleto</th>
                    </tr>
                  </thead>
                  <tbody>{contratosData.map(renderRow)}</tbody>
                </table>
              </div>
              <NegociacaoResumoModal
                isOpen={showResumoModal}
                onClose={() => setShowResumoModal(false)}
              />
              <NegociacoesModal
                isOpen={showNegociacaoModal}
                onClose={() => setShowNegociacaoModal(false)}
                dados={dadosNegociacao}
              />
            </>
          ) : (
            <CRow style={{ textAlign: "center" }}>
              <CCol className="mt-2">Dados dos contratos não encontrados.</CCol>
            </CRow>
          )}
          {showRniModal && (
            <TipoCalcRNI
              isOpen={showRniModal}
              onClose={handleCloseTipoRniModal}
            />
          )}
          {showRniCalcTipoUmModal && (
            <CalcTipoUmRNI
              isOpen={showRniCalcTipoUmModal}
              onClose={() => setShowRniCalcTipoUmModal(false)}
              contratosData={contratosData}
            />
          )}
          {showRniCalcTipoTresModal && (
            <CalcTipoTresRNI
              isOpen={showRniCalcTipoTresModal}
              onClose={() => setShowRniCalcTipoTresModal(false)}
              contratosData={contratosData}
            />
          )}
          {showRniCalcTipoDoisModal && (
            <CalcTipoDoisRNI
              isOpen={showRniCalcTipoDoisModal}
              onClose={() => setShowRniCalcTipoDoisModal(false)}
              contratosData={contratosData}
            />
          )}
          {showTermoJuridicoModal && (
            <TermoJuridicoModal
              isOpen={showTermoJuridicoModal}
              onClose={() => setShowTermoJuridicoModal(false)}
              contratos={contratosData}
              datacobData={calculoValues}
            />
          )}
        </>
      )}
    </>
  );
};

export default ContratosAtivos;
