import React, { useEffect, useState } from "react";
import { <PERSON>utton, CBadge } from "@coreui/react";
import { CCard, CCardBody, CCol, CRow, CTooltip, CInput } from "@coreui/react";
import { toast } from "react-toastify";

import CIcon from "@coreui/icons-react";
import CreateUserModal from "./CreateUserModal";
import SelecionarDatacobModal from "./SelecionarDatacobModal";
import { getURI } from "src/config/apiConfig";
import { postCredentials } from "src/config/telephonyFunctions";
import TableSelectItens from "src/reusable/TableSelectItens";
import { useAuth } from "src/auth/AuthContext";
import EditUserCrmAuthModal from "./Modal/EditUserCrmAuthModal.tsx";
import { token } from "src/api";

import "react-toastify/dist/ReactToastify.css";

const GerenciarUsuarios = () => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Configurações",
    submodulo: "Gerenciar Usuários",
  };

  const [showSelecionarModal, setShowSelecionarModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [originConnection, setOriginConnection] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [usersList, setUsersList] = useState([]);

  const [editUserCrmAuthModal, setEditUserCrmAuthModal] = useState(false);
  const [selectedUserCrms, setSelectedUserCrms] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
  };

  const handleCreateUser = async (newUser) => {
    try {
      const url = `${getURI()}/Auth/Register`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`
        },
        body: JSON.stringify(newUser),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {
          const payloadTelCredentials = {
            userId: status.data,
            username: newUser.userTel,
            password: newUser.passTel ?? null,
            telephonyId: newUser.telephonyId,
          };
          await postCredentials(payloadTelCredentials);
          toast.success("Usuário criado com sucesso");
          await getUsers(newUser.name);
        } else {
          toast.warning("Usuário já cadastrado");
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro cadastrando o usuário:", error);
    }
  };

  const fields = [
    {
      key: "name",
      label: "Usuário e email",
      formatterByObject: (item) => handleViewName(item),
    },
    {
      key: "groups",
      label: "Grupos",
      defaultSortColumn: true,
      defaultSort: "desc",
      formatterByObject: (item) => handleViewGrupos(item),
    },
    {
      key: "role",
      label: "Função",
      formatterByObject: (item) => handleViewRole(item),
    },
    {
      key: "active",
      label: "Status",
      formatterByObject: (item) => handleViewActive(item),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => handleViewActions(item),
    },
  ];

  const updateUser = async (user) => {
    try {
      const url = `${getURI()}/User/UpdateUser`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify({
          ...user,
          id: user.userId,
        }),
      });

      const status = await response.json();
      if (response.ok) {
        if (status.success) {         
          await postCredentials({
            userId: user.userId,
            username: user.userTel,
            password: user.passTel,
            telephonyId: user.telephonyId,
          });
          toast.success("Usuário editado com sucesso");
          await getUsers(user.name);
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro editando o usuário:", error);
    }
  };

  const getUsers = async (search) => {
    try {
      const response = await fetch(`${getURI()}/User/AllUsers?take=100&search=${search}`, {
        headers: {
          Authorization: `Bearer ${token()}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsersList(data.data);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const handleSaveEdit = (editedUser) => {
    updateUser(editedUser);
  };

  const handleEdit = (editUser) => {
    setSelectedUser(editUser);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedUser(null);
    setIsModalOpen(false);
  };

  const handleSelecionarDatacob = (origin) => {
    setOriginConnection(origin);
    setSelectedUser(null);
    setIsModalOpen(true);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        await getUsers("");
      } catch (error) {
      }
    };
    fetchData();
  }, []);

  const search = async (searchTerm) => {
    setIsLoading(true);
    try {
      await getUsers(searchTerm);
    } catch (error) {
      console.error("Erro durante a pesquisa:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewName = (item) => {
    return (
      <>
        <div style={{ display: "flex", alignItems: "center" }}>
          <div>
            <CTooltip content={item.name} placement="top">
              <CIcon
                name="cil-user"
                alt={item.name}
                className="rounded-circle mr-2"
                width="30"
                height="30"
              />
            </CTooltip>
          </div>{" "}
          {item.name}
          <br />
          {item.email}{" "}
        </div>
      </>
    );
  };

  const handleViewGrupos = (item) => {
    return (
      <>
        {" "}
        {item.groups.map((group) => (
          <CBadge key={group.id} color="secondary" className="mr-1">
            {group.name}
          </CBadge>
        ))}{" "}
      </>
    );
  };

  const handleViewRole = (item) => {
    return item.isAdmin ? (
      <span>Admnistrador</span>
    ) : item.role ? (
      <span>{item.role.name}</span>
    ) : (
      <></>
    );
  };

  const handleViewActive = (item) => {
    return item.active ? (
      <CBadge color="success">Ativo</CBadge>
    ) : (
      <CBadge color="danger">Inativo</CBadge>
    );
  };

  const handleViewActions = (item) => {
    return (
      <>
        <CButton
          color="secondary"
          onClick={() => handleEdit(item)}
          title={inforPermissions(permissao).edit}
          disabled={
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
          }
        >
          <i className="cil-pencil"></i>
        </CButton>
      </>
    );
  };

  return (
    <div>
      <CRow>
        <CCol className="align-items-center" md="5">
          <h2>Gerenciar Usuários</h2>
          <p style={{ color: "gray", fontSize: "small" }}>
            Gerencie seus usuários, defina seus grupos e funções personalizadas.
            Mantenha o controle total sobre as contas no sistema.
          </p>
        </CCol>
        <CCol md="3" style={{ padding: 0 }}>
          <CInput
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder={"Informe Nome ou Email"}
          />
        </CCol>
        <CCol md="1" style={{ padding: 0 }}>
          <CButton
            color="primary"
            onClick={() => search(searchTerm)}
            style={{ marginLeft: "0.5rem" }}
            disabled={isLoading}
          >
            {isLoading ? (
              <i className="cil-reload" />
            ) : (
              <i className="cil-search" />
            )}
          </CButton>
        </CCol>
        <CCol className="text-right" md="3">
          <CButton
            color="info"
            onClick={() => setShowSelecionarModal(true)}
            title={inforPermissions(permissao).create}
            disabled={
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            <i className={"cil-user"} /> Adicionar usuário
          </CButton>
        </CCol>
      </CRow>
      <CCard style={{ height: "100hv" }}>
        <CCardBody>
          <TableSelectItens
            data={usersList}
            columns={fields}
            onSelectionChange={(_) => { }}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="100%"
          />
        </CCardBody>
      </CCard>
      <SelecionarDatacobModal
        isOpen={showSelecionarModal}
        onConfirm={handleSelecionarDatacob}
        onClose={() => setShowSelecionarModal(false)}
      />
      {isModalOpen && (
        <CreateUserModal
          isOpen={isModalOpen}
          originConnection={originConnection}
          userList={usersList}
          onCreateUser={handleCreateUser}
          onEditUser={handleSaveEdit}
          editUser={selectedUser}
          onClose={handleCloseModal}
          onEditUserCrmAuth={() => {
            setEditUserCrmAuthModal(true);
          }}
        />
      )}
      {editUserCrmAuthModal && (
        <EditUserCrmAuthModal
          editUser={selectedUser}
          onClose={() => {
            setEditUserCrmAuthModal(false);
            setSelectedUserCrms(selectedUserCrms);
            setIsModalOpen(true);
          }}
        />
      )}
    </div>
  );
};

export default GerenciarUsuarios;
