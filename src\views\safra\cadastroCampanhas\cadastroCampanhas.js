import React, { useEffect, useState } from 'react';
import { CButton, CCard, CCardBody, CCol, CDataTable, CForm, CInput, CLabel, CRow } from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import { formatCurrency } from "src/reusable/helpers";
import { getURI } from "src/config/apiConfig";
import { getApi, postApi } from "src/reusable/functions";
import { POST_FORMDATA } from "src/api";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { toast } from "react-toastify";
import { useAuth } from "src/auth/AuthContext";
import { MountURI } from "src/api";
import Select from "react-select";
import { token } from "src/api";

const PostFormData = async (formData, endpoint = "", id = "") => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_FORMDATA(
        getURI(endpoint),
        formData,
        true,
        true,
        id
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

const filtersValuesDefault = {
  campaignName: "",
  numberInstallmentsDeal: 0,
  daysInDelay: 0,
  kindOfContracts: "",
  numberInstallments: 0,
  campaignDateStart: new Date().toJSON().slice(0, 10),
  campaignDateEnd: new Date().toJSON().slice(0, 10),
  file: "file",
};

const filterRequiredFields = [
  "numberInstallments",
  "kindOfContracts",
  "daysInDelay",
];

const columns = [
  {
    key: "name",
    label: "Nome do Devedor",
  },
  {
    key: "document",
    label: "CPF/CNPJ",
  },
  {
    key: "contract",
    label: "Contrato",
  },
  {
    key: "installmentAmount",
    label: "QTD de parcelas",
  },
  {
    key: "installmentTotalValue",
    label: "Valor total das Parcelas",
  },
  {
    key: "action",
    label: "Ações",
  },
];

const typesDue = [
  {
    id: 0,
    descricao: "D + 0",
  },
  {
    id: 1,
    descricao: "D + 1",
  },
  {
    id: 2,
    descricao: "D + 2",
  },
  {
    id: 3,
    descricao: "D + 3",
  },
  {
    id: 4,
    descricao: "D + 4",
  },
  {
    id: 5,
    descricao: "D + 5",
  }
];

const CadastroCampanhas = () => {
  const [campaigns, setCampaigns] = useState([]);
  const [kindOfRegister, setKindOfRegister] = useState("dataTable");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingExcel, setIsLoadingExcel] = useState(false);

  const [filters, setFilters] = useState(filtersValuesDefault);
  const [excelFile, setExcelFile] = useState(null);

  const [errors, setErrors] = useState({});

  const [selectedFrase, setSelectedFrase] = useState(null);
  const [fraseOptions, setFraseOptions] = useState([]);
  const history = useHistory();
  const [selectedTypeDue, setSelectedTypeDue] = useState(0);
  const [typeDueOptions, setTypeDueOptions] = useState([]);

  const { checkPermission } = useAuth();
  const permissao = {
    modulo: "Campanhas Safra",
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };

  const handleInputIntChange = (e) => {
    const { name, value } = e.target;
    const valueNumber = parseInt(value);

    setFilters({
      ...filters,
      [name]: isNaN(valueNumber) || valueNumber < 0 ? 0 : valueNumber,
    });
  };

  const handleUploadFile = async (event) => {
    const file = event.target.files;
    setExcelFile(file[0]);
  };

  const handleSelectFrase = (selectedOption) => {
    setSelectedFrase(selectedOption);
  };

  const handleSelectTypeDue = (selectedOption) => {
    setSelectedTypeDue(selectedOption);
  };

  useEffect(() => {
    setSelectedFrase(null);
    getFrases();
    setSelectedTypeDue(null);
    setTypeDueOptions(typesDue);
  }, []);

  const validate = () => {
    let isValid = true;
    for (const key in filters) {
      if (filters[key] === "" || filters[key] === 0) {
        setErrors((prev) => ({ ...prev, [key]: "Campo Obrigatório" }));
        if (kindOfRegister === "excel" && !filterRequiredFields.includes(key)) {
          isValid = false;
        } else if (
          kindOfRegister !== "excel" &&
          filterRequiredFields.includes(key)
        ) {
          isValid = false;
        }
      } else {
        setErrors((prev) => ({ ...prev, [key]: "" }));
      }
    }
    if (kindOfRegister === "excel" && !excelFile) {
      setErrors((prev) => ({ ...prev, file: "Campo Obrigatório" }));
    }
    return isValid;
  };

  async function createCampaign() {
    setIsLoading(true);
    if (validate())
      if (kindOfRegister === "excel") {
        const response = await PostFormData(
          mountBodyToSendCampaignData(),
          checkURLToSendCampaignData()
        );
        if (response.success && response.data?.length > 0) {
          setCampaigns(response.data);
        } else {
          setCampaigns([]);
          toast.warning("Nenhum resultado encontrado!");
        }
      } else {
        const responseFilter = await getApi(
          mountBodyToSendCampaignData(),
          checkURLToSendCampaignData()
        );
        if (responseFilter?.length > 0) {
          setCampaigns(responseFilter);
        } else {
          setCampaigns([]);
          toast.warning("Nenhum resultado encontrado!");
        }
      }
    // const response = await fetch(`${checkURLToSendCampaignData()}`, {
    //   method: "POST",
    //   headers: {
    //     Authorization: `Bearer ${token()}`,
    //   },
    //   body: JSON.stringify(mountBodyToSendCampaignData()),
    // });

    setIsLoading(false);
  }

  function checkURLToSendCampaignData() {
    if (kindOfRegister !== "excel") {
      return `getSafraCampaignFilter`;
    } else {
      return `getSafraCampaignFilterExcel`;
    }
  }
  function checkURLToSendCampaignDataExportToExcel() {
    if (kindOfRegister !== "excel") {
      return `getSafraCampaignFilterExportToExcel`;
    } else {
      return `getSafraCampaignFilterExcelExportToExcel`;
    }
  }

  function mountBodyToSendCampaignData() {
    if (kindOfRegister === "excel") {
      const formData = new FormData();
      formData.append("file", excelFile);
      return formData;
    } else {
      return {
        daysDelay: filters.daysInDelay,
        amountInstall: filters.numberInstallments,
        type: filters.kindOfContracts,
      };
    }
  }

  const handleTrashList = (item) => {
    setCampaigns(campaigns.filter((x) => x.contractId !== item.contractId));
  };

  const handleConfirmCampaign = async () => {
    if (campaigns.length <= 0) return false;
    const result = await postApi(
      {
        name: filters.campaignName,
        beginDate: filters.campaignDateStart,
        endDate: filters.campaignDateEnd,
        installmentAmount: filters.numberInstallmentsDeal,
        safraCampaignDueType: (selectedTypeDue?.id ?? 0),
        itens: campaigns.map((x) => ({
          contractId: x.contractId,
          financedId: x.financedId,
          installmentTotalValue: x.installmentTotalValue,
          contract: x.contract,
          installmentAmount: x.installmentAmount,
        }
        )),
        restrictions: selectedFrase?.map((x) => ({
          corJuridicoParamId: x.id
        })),
      },
      "restSafraCampaign"
    );
    if (result?.success) {
      history.push("/safraCampanhas");
    } else {
      toast.error("Falha ao gerar campanha!");
    }
  };

  const getFrases = async () => {
    const data = {};
    const url = MountURI("CorJuridicoParam", data);

    try {
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token()}`,
        },
      });

      if (response.ok) {
        const responseData = await response.json();
        const fraseOptions = responseData.data.map((x) => ({
          id: x.id,
          descricao: x.name,
        }));
        setFraseOptions(fraseOptions);
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando frases jurídicas:", error);
    }
  };

  const exportCampaign = async () => {
    setIsLoadingExcel(true);
    if (validate())
      if (kindOfRegister === "excel") {
        const response = await PostFormData(
          mountBodyToSendCampaignData(),
          checkURLToSendCampaignDataExportToExcel()
        );
        if (response.size > 0) {
          const blob = await response;
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "campanha.xlsx";
          document.body.appendChild(link); // Necessário para o Firefox
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url); // Limpa o objeto URL
        } else {
          toast.warning("Nenhum resultado encontrado!");
        }
      } else {
        const responseFilter = await getApi(
          mountBodyToSendCampaignData(),
          checkURLToSendCampaignDataExportToExcel()
        );
        if (responseFilter.size > 0) {
          const blob = await responseFilter;
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "campanha.xlsx";
          document.body.appendChild(link); // Necessário para o Firefox
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url); // Limpa o objeto URL
        } else {
          toast.warning("Nenhum resultado encontrado!");
        }
      }
    setIsLoadingExcel(false);
  };

  return (
    <div>
      <h3>Cadastro de Campanha</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        {checkPermission(permissao.modulo, "Create") ? (
          <>
            <CCard>
              <CCardBody>
                <CRow>
                  <CCol md={3}>
                    <CLabel>Campanha Nome:</CLabel>
                    <CInput
                      name="campaignName"
                      value={filters.campaignName}
                      onChange={handleInputChange}
                    />
                    {errors.campaignName && (
                      <small className="form-text text-danger font-weight-bold">
                        {errors.campaignName}
                      </small>
                    )}
                  </CCol>
                  <CCol md={3}>
                    <CLabel>Qtd de parcelas do acordo:</CLabel>
                    <CInput
                      type="text"
                      name="numberInstallmentsDeal"
                      value={filters.numberInstallmentsDeal}
                      onChange={handleInputIntChange}
                    />
                    {errors.numberInstallmentsDeal && (
                      <small className="form-text text-danger font-weight-bold">
                        {errors.numberInstallmentsDeal}
                      </small>
                    )}
                  </CCol>
                  <CCol md={3}>
                    <CLabel>Data Inicial da campanha:</CLabel>
                    <CInput
                      type="date"
                      name="campaignDateStart"
                      value={filters.campaignDateStart}
                      onChange={handleInputChange}
                    />
                    {errors.campaignDateStart && (
                      <small className="form-text text-danger font-weight-bold">
                        {errors.campaignDateStart}
                      </small>
                    )}
                  </CCol>
                  <CCol md={3}>
                    <CLabel>Data Final da campanha:</CLabel>
                    <CInput
                      type="date"
                      name="campaignDateEnd"
                      value={filters.campaignDateEnd}
                      onChange={handleInputChange}
                    />
                    {errors.campaignDateEnd && (
                      <small className="form-text text-danger font-weight-bold">
                        {errors.campaignDateEnd}
                      </small>
                    )}
                  </CCol>
                </CRow>
                <br />
                <CRow className="ml-1">
                  <CLabel>Tipos de Dados:</CLabel>
                  <div className="d-flex justify-content-around">
                    <CCol md={5}>
                      <input
                        className="form-check-input"
                        type="radio"
                        name="tipoDeDados"
                        id="dataTable"
                        checked={kindOfRegister === "dataTable"}
                        onChange={() => setKindOfRegister("dataTable")}
                      />
                      <label className="form-check-label" htmlFor="dataTable">
                        Filtro
                      </label>
                    </CCol>
                    <CCol md={1}>
                      <input
                        className="form-check-input"
                        type="radio"
                        name="tipoDeDados"
                        id="excel"
                        checked={kindOfRegister === "excel"}
                        onChange={() => setKindOfRegister("excel")}
                      />
                      <label className="form-check-label" htmlFor="excel">
                        Excel
                      </label>
                    </CCol>
                  </div>
                </CRow>
                <CRow>
                  {kindOfRegister === "excel" ? (
                    <CCol md={4}>
                      <CLabel>Fazer upload de arquivo Excel:</CLabel>
                      <CInput
                        type="file"
                        name="excelFile"
                        onChange={handleUploadFile}
                      />
                      {errors.file && (
                        <small className="form-text text-danger font-weight-bold mt-3">
                          {errors.file}
                        </small>
                      )}
                    </CCol>
                  ) : (
                    <>
                      <CCol md={2}>
                        <CLabel>Dias de Atraso:</CLabel>
                        <CInput
                          type="text"
                          name="daysInDelay"
                          value={filters.daysInDelay}
                          onChange={handleInputIntChange}
                        />
                        {errors.daysInDelay && (
                          <small className="form-text text-danger font-weight-bold">
                            {errors.daysInDelay}
                          </small>
                        )}
                      </CCol>
                      <CCol md={2}>
                        <CLabel>Quantidade de Parcelas:</CLabel>
                        <CInput
                          type="text"
                          name="numberInstallments"
                          value={filters.numberInstallments}
                          onChange={handleInputIntChange}
                        />
                        {errors.numberInstallments && (
                          <small className="form-text text-danger font-weight-bold">
                            {errors.numberInstallments}
                          </small>
                        )}
                      </CCol>
                      <CCol md="3">
                        <CLabel>Tipos de Contrato:</CLabel>
                        <div className="d-flex justify-content-around">
                          <CCol md={2}>
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoDeContrato"
                              id="contratoLeve"
                              onChange={() =>
                                setFilters({
                                  ...filters,
                                  kindOfContracts: "Leves",
                                })
                              }
                            />
                            <label
                              className="form-check-label"
                              htmlFor="contratoLeve"
                            >
                              Leves
                            </label>
                          </CCol>
                          <CCol md={2}>
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoDeContrato"
                              id="contratoPesado"
                              onChange={() =>
                                setFilters({
                                  ...filters,
                                  kindOfContracts: "Pesados",
                                })
                              }
                            />
                            <label
                              className="form-check-label"
                              htmlFor="contratoPesado"
                            >
                              Pesados
                            </label>
                          </CCol>
                          <CCol md={2}>
                            <input
                              className="form-check-input"
                              type="radio"
                              name="tipoDeContrato"
                              id="ambos"
                              onChange={() =>
                                setFilters({
                                  ...filters,
                                  kindOfContracts: "Ambos",
                                })
                              }
                            />
                            <label className="form-check-label" htmlFor="ambos">
                              Ambos
                            </label>
                          </CCol>
                        </div>
                        {errors.kindOfContracts && (
                          <small className="form-text text-danger font-weight-bold mt-3">
                            {errors.kindOfContracts}
                          </small>
                        )}
                      </CCol>
                    </>
                  )}
                  <CCol md="4">
                    <CLabel>Tipo do Vencimento</CLabel>
                    <Select
                      placeholder="Selecione tipo do vencimento"
                      value={selectedTypeDue}
                      onChange={handleSelectTypeDue}
                      options={typeDueOptions}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.descricao}
                    />
                  </CCol>
                  <CCol md="4">
                    <CLabel>Selecione Frases Jurídicas que devem ser desconsideradas:</CLabel>
                    <Select
                      placeholder="Selecione uma ou mais Frases"
                      value={selectedFrase}
                      onChange={handleSelectFrase}
                      options={fraseOptions}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.descricao}
                      isMulti
                    />
                  </CCol>
                </CRow>
                <CRow>
                  <CCol md={12} className="d-flex justify-content-end mt-4">
                    <CButton
                      color="info mr-3"
                      onClick={exportCampaign}
                      disabled={isLoadingExcel}
                    >
                      <i className="cil-file mr-1"></i>
                      {isLoadingExcel ? "Exportando..." : "Exportar"}
                    </CButton>
                    <CButton
                      color="warning"
                      className="text-white mr-3"
                      onClick={() => setFilters(filtersValuesDefault)}
                    >
                      Limpar filtros
                    </CButton>
                    <CButton
                      color="success"
                      onClick={() => createCampaign()}
                      disabled={isLoading}
                    >
                      {isLoading ? "Gerando..." : "Gerar lista"}
                    </CButton>
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
            {/* Lista Clientes / Campanha */}
            {campaigns.length > 0 && (
              <CCard>
                <CCardBody>
                  <CForm>
                    <CRow>
                      <CCol xs>
                        <CRow>
                          {isLoading ? (
                            <CardLoading />
                          ) : (
                            <CCol>
                              <CDataTable
                                items={campaigns}
                                fields={columns}
                                scopedSlots={{
                                  installmentTotalValue: (item) => (
                                    <td>
                                      {formatCurrency(
                                        item?.installmentTotalValue,
                                        true
                                      )}
                                    </td>
                                  ),
                                  action: (item) => (
                                    <td>
                                      <CButton
                                        color="primary"
                                        onClick={() => handleTrashList(item)}
                                      >
                                        <i className="cil-trash"></i>
                                      </CButton>
                                    </td>
                                  ),
                                }}
                                pagination
                                itemsPerPage={10}
                                heightParam="600px"
                              />
                            </CCol>
                          )}
                        </CRow>
                      </CCol>
                    </CRow>
                  </CForm>
                  <CRow>
                    <CCol md={3} className="d-flex justify-content-start mt-4">
                      <h5>Total Registros: {campaigns.length}</h5>
                    </CCol>
                    <CCol md={9} className="d-flex justify-content-end mt-4">
                      <CButton
                        color="danger"
                        onClick={() => setCampaigns([])}
                        className="ml-3 mr-3"
                      >
                        Descartar campanha
                      </CButton>
                      <CButton color="success" onClick={handleConfirmCampaign}>
                        Confirmar e ativar campanha
                      </CButton>
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
            )}
          </>
        ) : (
          <CCard>
            <CCardBody className={"text-center"}>
              Usuário não possui permissão de criação de campanhas.
            </CCardBody>
          </CCard>
        )}
      </div>
    </div>
  );
};

export default CadastroCampanhas;
