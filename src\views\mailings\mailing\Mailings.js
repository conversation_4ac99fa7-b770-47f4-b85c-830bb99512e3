import React, { useEffect, useState, useCallback } from "react";
import "react-toastify/dist/ReactToastify.css";
import { CButton, CBadge, CCard, CCardBody, CCol, CRow, CInput } from "@coreui/react";
import TableSelectItens from "src/reusable/TableSelectItens";
import CreateMailingModal from "./CreateMailingModal";
import { getURI } from "src/config/apiConfig";
import { GET_DATA, token } from "src/api";
import { useAuth } from "src/auth/AuthContext";
import { formatDate } from "src/reusable/helpers";

const Mailings = () => {
  const { inforPermissions, checkPermission } = useAuth();
  const permissao = {
    modulo: "Mailing",
    submodulo: "Gerenciar Mailings",
  };

  const [selectedMailing, setSelectedMailing] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mailingsList, setMailingsList] = useState([]);

  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [crmsOptions, setCrmsOptions] = useState([]);
  const [phaseOptions, setPhaseOptions] = useState([]);
  const statusOptions = [
    { id: "Create", descricao: "Criado" },
    { id: "Started", descricao: "Iniciado" },
    { id: "Completed", descricao: "Concluído" },
    { id: "Canceled", descricao: "Cancelado" },
  ];

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
  };

  const fields = [
    {
      key: "name",
      label: "Título",
    },
    {
      key: "startDate",
      label: "Data Inicio",
      defaultSort: "desc",
      defaultSortColumn: true,
      formatter: (value) => formatDate(value),
    },
    {
      key: "crmId",
      label: "CRM",
      formatterByObject: (item) => handleViewCrm(item),
    },
    {
      key: "groupId",
      label: "Grupo CRM",
      formatterByObject: (item) => handleViewGrupoCrm(item),
    },
    {
      key: "faseId",
      label: "fase",
      formatterByObject: (item) => handleViewPhase(item),
    },
    {
      key: "status",
      label: "Situação",
      formatterByObject: (item) => handleViewStatus(item),
    },
    {
      key: "active",
      label: "Status",
      formatterByObject: (item) => handleViewActive(item),
    },
    {
      key: "actions",
      label: "Editar",
      formatterByObject: (item) => handleViewActions(item),
    },
  ];

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getMailings = useCallback(
    async (search) => {
      try {
        const response = await fetch(`${getURI()}/Mailing?search=${search}`, {
          headers: { Authorization: `Bearer ${token()}` },
        });
        if (response.ok) {
          const data = await response.json();
          setMailingsList(data.data || []);
          return data.data || [];
        } else {
          console.error("Erro:", response?.statusText);
          return [];
        }
      } catch (error) {
        console.error("Erro buscando usuários:", error);
        return [];
      }
    },
    [token]
  );

  const handleEdit = (editMailing) => {
    editMailing["crm"] = crmsOptions;
    editMailing["phase"] = phaseOptions;
    editMailing["statusList"] = statusOptions;

    setSelectedMailing(editMailing);
    setIsModalOpen(true);
  };

  const handleCreate = () => {
    let editMailing = {
      crm: crmsOptions,
      phase: phaseOptions,
      statusList: statusOptions,
    };
    setSelectedMailing(editMailing);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    getMailings("").then(() => {
      setSelectedMailing(null);
      setIsModalOpen(false);
    });
  };

  const getPhases = useCallback(() => {
    return GetData({}, "getGroupPhases")
      .then((res) => {
        if (res && res !== null && res !== undefined) {
          return res;
        }
      })
      .catch((err) => {
        console.warn(err);
        return null;
      });
  }, []);

  const getCrms = useCallback(() => {
    setIsLoading(true);
    return GetData({}, "getCrms")
      .then((res) => {
        if (res && res !== null && res !== undefined) {
          setIsLoading(false);
          return res;
        } else setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.warn(err);
        return null;
      });
  }, []);

  const getGrupoCrm = useCallback((crm) => {
    setIsLoading(true);
    return GetData({ ActiveConnection: crm }, "getGrupoDataCobLista")
      .then((res) => {
        if (res && res !== null && res !== undefined) {
          setIsLoading(false);
          return res;
        } else setIsLoading(false);
      })
      .catch((err) => {
        console.warn(err);
        setIsLoading(false);
        return null;
      });
  }, []);

  const fetchGroupsForCrm = useCallback(
    (crm) => {
      return getGrupoCrm(crm.datacobName).then((groupData) => {
        crm.grupos = groupData;
        return crm;
      });
    },
    [getGrupoCrm]
  );

  useEffect(() => {
    getCrms()
      .then((crms) => {
        if (crms) {
          return Promise.all(crms.map(fetchGroupsForCrm));
        }
        return [];
      })
      .then((crmGroupData) => {
        setCrmsOptions(crmGroupData);
        return getPhases();
      })
      .then((phasesData) => {
        setPhaseOptions(phasesData);
        return getMailings("");
      })
      .catch((err) => {
        console.warn(err);
      });
  }, [getCrms, fetchGroupsForCrm, getPhases, getMailings]);

  const search = async (searchTerm) => {
    setIsLoading(true);
    try {
      getMailings(searchTerm);
    } catch (error) {
      console.error("Erro durante a pesquisa:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewCrm = (item) => {
    const crm = crmsOptions?.find((a) => a.id === item?.crmId);
    const crmName = crm?.datacobName || "N/A";
    return crmName;
  };
  const findGroupInMultipleIds = (grupo, itemGroupId) => {
    if (!grupo || !grupo.id_Grupo) return false;

    // Divide a string de IDs, remove espaços e converte para números
    const idsArray = itemGroupId
      .split(",")
      .map((id) => parseInt(id.trim()))
      .filter((id) => !isNaN(id)); // Remove valores inválidos

    // Verifica se o itemGroupId está na lista de IDs
    return idsArray.includes(grupo.id_Grupo);
  };
  const handleViewGrupoCrm = (item) => {
    const crm = crmsOptions?.find((a) => a.id === item?.crmId);

    const matchingGroups =
      crm?.grupos?.filter((a) => findGroupInMultipleIds(a, item.groupId)) || [];

    // Concatena todas as descrições encontradas ou retorna "N/A" se nenhuma for encontrada
    const groupName =
      matchingGroups.length > 0
        ? matchingGroups.map((g) => g.descricao).join(", ")
        : "N/A";
    return groupName;
  };

  const handleViewPhase = (item) => {
    return item.faseId
      ? phaseOptions?.find((a) => a.cod_Fase === item.faseId)?.descricao
      : "";
  };

  const handleViewStatus = (item) => {
    return item.status
      ? statusOptions?.find((a) => a.id === item.status)?.descricao
      : "N/A";
  };

  const handleViewActive = (item) => {
    return item.active ? (
      <CBadge color="success">Ativo</CBadge>
    ) : (
      <CBadge color="danger">Inativo</CBadge>
    );
  };

  const handleViewActions = (item) => {
    return (
      <>
        <CButton
          color="secondary"
          onClick={() => handleEdit(item)}
          title={inforPermissions(permissao).edit}
          disabled={
            !checkPermission(permissao.modulo, "Edit", permissao.submodulo)
          }
        >
          <i className="cil-pencil"></i>
        </CButton>
      </>
    );
  };

  return (
    <div>
      <CRow>
        <CCol className="align-items-center" md="5">
          <h2>Mailings</h2>
          <p style={{ color: "gray", fontSize: "small" }}>
            Crie um registro de registros Mailing.
          </p>
        </CCol>
        <CCol md="3" style={{ padding: 0 }}>
          <CInput
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder={"Informe Título"}
          />
        </CCol>
        <CCol md="1" style={{ padding: 0 }}>
          <CButton
            color="primary"
            onClick={() => search(searchTerm)}
            style={{ marginLeft: "0.5rem" }}
            disabled={isLoading}
          >
            {isLoading ? (
              <i className="cil-reload" />
            ) : (
              <i className="cil-search" />
            )}
          </CButton>
        </CCol>
        <CCol className="text-right" md="3">
          <CButton
            color="info"
            onClick={() => handleCreate()}
            title={inforPermissions(permissao).create}
            disabled={
              isLoading ||
              !checkPermission(permissao.modulo, "Create", permissao.submodulo)
            }
          >
            <i className={"cil-plus"} /> Adicionar
          </CButton>
        </CCol>
      </CRow>
      <CCard style={{ height: "100hv" }}>
        <CCardBody>
          <TableSelectItens
            data={mailingsList}
            columns={fields}
            onSelectionChange={(_) => { }}
            defaultSelectedKeys={[]}
            selectable={false}
            heightParam="100%"
          />
        </CCardBody>
      </CCard>
      {isModalOpen && (
        <CreateMailingModal
          isOpen={isModalOpen}
          editMailing={selectedMailing}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default Mailings;
